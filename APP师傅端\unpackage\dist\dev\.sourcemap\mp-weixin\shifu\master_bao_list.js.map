{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?1a32", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?6a19", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?0df9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?2c7d", "uni-app:///shifu/master_bao_list.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?a62c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_bao_list.vue?5248"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "goodsId", "page", "pageSize", "list", "show", "input", "quoteMessage", "shifuId", "id", "userInfo", "showCancel", "scrollToId", "loadingStatus", "loadingText", "hasMore", "total", "tabId", "fromQuote", "isRefreshing", "isLoadingMore", "onPullDownRefresh", "console", "onReachBottom", "onShow", "methods", "refreshData", "uni", "loadMoreData", "getList", "coachId", "pageNum", "setTimeout", "resolve", "icon", "title", "reject", "cancelBao", "cancelModal", "confirm", "again<PERSON><PERSON>", "close", "confirmBao", "orderId", "price", "onUnload", "app", "delta", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmGl3B;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACA;EACAC;IACAC;IACA;EACA;EACA;EACAC;IACAD;IACA;MACA;MACA;IACA;EACA;EACA;EACAE;IACAF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAG;IACA;IACAC;MAAA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;MACA;QACA;UACAC;UAEAC;UACA5B;QACA;UACAmB;UACA;YACA;YACA;YACA;YACA;YACA;YACAU;cACA;YACA;YACAC;YACA;UACA;UACA;UACA;;UAEA;UACA;YAAA;UAAA;UAEA;YACA;YACA;UACA;YACA;YACA;UACA;;UAEA;UACA;;UAEA;UACA;YACA;YACA;YACAD;cACA;YACA;UACA;UAEAC;QACA;UACAX;;UAEA;UACA;YACA;UACA;;UAEA;UACA;UACAK;YACAO;YACAC;UACA;UAEAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;QACA9B;MACA;QACAa;QACA;UACAK;YACAO;YACAC;UACA;UACA;QACA;UACAR;YACAO;YACAC;UACA;QACA;;QAEA;QACA;MACA;QACA;QACAR;UACAO;UACAC;QACA;MACA;IACA;IAEA;IACAK;MAAA;MACAlB;MACA;MACA;MACA;MACA;MACA;MACAU;QACA;MACA;IACA;IAEA;IACAS;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAf;UACAO;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAR;UACAO;UACAC;QACA;QACA;MACA;MAEA;MACA;MAEA;QACAQ;QACAC;QACA3C;QACAM;MACA;QACAe;QACA;UACAK;YACAO;YACAC;UACA;QAEA;UACAb;UACAK;YACAO;YACAC;UACA;UACA;UACA;UACA;QACA;MACA;QACA;QACAR;UACAO;UACAC;QACA;QACA;MACA;IACA;EACA;EACA;EACA;EACA;EACAU;IACAvB;MACAL;MACAC;IACA;IACA;IACA;MACAI;MACA;MACAK;MACAL;MACA;MACA;MACA;QACAwB;MACA;MACA;MACAnB;MAEAA;QACAoB;MACA;IAEA;EACA;EACA;EACAC;IAAA;IACA1B;IACA;IACA;IACA;IACAA;IACA;MACA;MACA;QACA;QACA;QACAA;MACA;MAEA;MACAA;;MAEA;MACA;MACA;MACA;QACA;MACA;IACA;MACAA;MACAK;QACAO;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChbA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_bao_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_bao_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_bao_list.vue?vue&type=template&id=0cdcf780&scoped=true&\"\nvar renderjs\nimport script from \"./master_bao_list.vue?vue&type=script&lang=js&\"\nexport * from \"./master_bao_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_bao_list.vue?vue&type=style&index=0&id=0cdcf780&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0cdcf780\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_bao_list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=template&id=0cdcf780&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length === 0 && !_vm.loadingStatus\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"main\">\n\t\t\t<!-- Order list with dynamic rendering -->\n\t\t\t<view class=\"main_item_already\" v-for=\"(item, index) in list\" :key=\"index\">\n\t\t\t\t<!-- Display order status based on payType -->\n\t\t\t\t<view style=\"display: flex; justify-content: space-between;\" class=\"\">\n\t\t\t\t\t<view class=\"title\">{{ item.payType == -2 ? '等待客户选择' : '' }}</view>\n\t\t\t\t\t<view class=\"title\">{{ item.payType == -1 ? '客户已取消订单' : '' }}</view>\n\t\t\t\t\t<view class=\"title\">{{ item.payType == 1 ? '客户已选择报价' : '' }}</view>\n\t\t\t\t\t<view style=\"padding-right: 40rpx; \" class=\"\">\n\t\t\t\t\t\t剩余报价次数:   {{item.remainingNum}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t<!-- Indicate that a bid has been placed -->\n\t\t\t\t<view class=\"ok\">您已报价</view>\n\t\t\t\t<!-- Display order number -->\n\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t<!-- Order details: image and name -->\n\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- Order timestamp -->\n\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t<text>{{item.orderTime}}</text>\n\t\t\t\t</view>\n\t\t\t\t<!-- Service provider info and bid price -->\n\t\t\t\t<view class=\"shifu\">\n\t\t\t\t\t<scroll-view scroll-x=\"true\">\n\t\t\t\t\t\t<view class=\"shifu_item\">\n\t\t\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t\t\t<image :src=\"userInfo.avatarUrl?userInfo.avatarUrl:'/static/mine/default_user.png'\" mode=\"\"></image>\n\t\t\t\t\t\t\t\t<view class=\"info\">\n\t\t\t\t\t\t\t\t\t<view class=\"name\">{{ userInfo.nickName }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text>￥{{ item.price }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 显示留言内容 -->\n\t\t\t\t<view class=\"quote-message\" v-if=\"item.quoteMessage\">\n\t\t\t\t\t<view class=\"message-label\">留言：</view>\n\t\t\t\t\t<view class=\"message-content\">{{ item.quoteMessage }}</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- Buttons for canceling or re-bidding, shown only if payType is -2 (awaiting selection) -->\n\t\t\t\t<view class=\"btnbox\" v-if=\"item.payType == -2\">\n\t\t\t\t\t<view class=\"btn can\" @click=\"cancelBao(item)\">取消报价</view>\n\t\t\t\t\t<view class=\"btn re\" @click=\"againBao(item)\">重新报价</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- Loading status indicator -->\n\t\t\t<view class=\"loading-status\" v-if=\"loadingStatus\">\n\t\t\t\t<text>{{ loadingText }}</text>\n\t\t\t</view>\n\n\t\t\t<!-- Empty data message -->\n\t\t\t<view class=\"empty-data\" v-if=\"list.length === 0 && !loadingStatus\">\n\t\t\t\t<text>暂无数据</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Popup for re-bidding -->\n\t\t<u-popup :show=\"show\" :round=\"10\" closeable @close=\"close\" :adjust-position=\"true\" mode=\"bottom\">\n\t\t\t<scroll-view scroll-y=\"true\" class=\"popup-scroll\" :scroll-into-view=\"scrollToId\">\n\t\t\t\t<view class=\"box\" id=\"input-box\">\n\t\t\t\t\t<view class=\"title\">重新报价</view>\n\t\t\t\t\t<view class=\"title2\">报价金额</view>\n\t\t\t\t\t<view class=\"money\">\n\t\t\t\t\t\t<u--input id=\"price-input\" placeholder=\"请输入报价金额\" prefixIcon=\"rmb\"\n\t\t\t\t\t\t\tprefixIconStyle=\"font-size: 22px;color: #909399\" type=\"digit\" v-model=\"input\"\n\t\t\t\t\t\t\tfocus></u--input>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 留言输入框 -->\n\t\t\t\t\t<view class=\"message-input-section\">\n\t\t\t\t\t\t<view class=\"message-label\">留言（选填）</view>\n\t\t\t\t\t\t<view class=\"message-input\">\n\t\t\t\t\t\t\t<u--input placeholder=\"请输入留言内容\" type=\"textarea\" v-model=\"quoteMessage\"\n\t\t\t\t\t\t\t\tmaxlength=\"200\" :autoHeight=\"true\" :height=\"120\"></u--input>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn\" @click=\"confirmBao\">确认报价</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</u-popup>\n\n\t\t<!-- Modal for confirming bid cancellation -->\n\t\t<u-modal :show=\"showCancel\" title=\"取消报价\" content=\"确定要取消对本单的报价吗？\" showCancelButton @cancel=\"cancelModal\"\n\t\t\t@confirm=\"confirm\"></u-modal>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tgoodsId:0,\n\t\t\t\tpage: 1, // Current page number\n\t\t\t\tpageSize: 10, // Number of items per page\n\t\t\t\tlist: [], // List of orders\n\t\t\t\tshow: false, // Controls re-bid popup visibility\n\t\t\t\tinput: '', // Re-bid input value\n\t\t\t\tquoteMessage: '', // 留言输入内容\n\t\t\t\tshifuId: '', // Service provider ID\n\t\t\t\tid: '', // Current order ID for actions\n\t\t\t\tuserInfo: '', // User information\n\t\t\t\tshowCancel: false, // Controls cancel modal visibility\n\t\t\t\tscrollToId: '', // ID for scrolling in popup\n\t\t\t\tloadingStatus: false, // Loading state flag\n\t\t\t\tloadingText: '', // Loading message\n\t\t\t\thasMore: true, // Whether more data is available\n\t\t\t\ttotal: 0, // Total number of orders\n\t\t\t\ttabId:\"0\",\n\t\t\t\tfromQuote: false, // 标识是否从报价页面跳转过来\n\t\t\t\tisRefreshing: false, // Prevents multiple refreshes\n\t\t\t\tisLoadingMore: false // Prevents multiple load more requests\n\t\t\t};\n\t\t},\n\t\t// Handle pull-down refresh\n\t\tonPullDownRefresh() {\n\t\t\tconsole.log('refresh');\n\t\t\tthis.refreshData();\n\t\t},\n\t\t// Handle reaching the bottom of the page\n\t\tonReachBottom() {\n\t\t\tconsole.log('reach bottom');\n\t\t\tif (this.list.length < this.total && !this.isLoadingMore) {\n\t\t\t\tthis.page += 1;\n\t\t\t\tthis.loadMoreData();\n\t\t\t}\n\t\t},\n\t\t// Handle page show\n\t\tonShow() {\n\t\t\tconsole.log('page show');\n\t\t},\n\t\t// onUnload() {\n\t\t// \tuni.setStorageSync('refreshReceiving', true);\n\t\t// \tthis.$store.commit('setRefreshReceiving', true);\n\t\t// \t\tuni.navigateBack({\n\t\t// \t\t\tdelta:1\n\t\t// \t\t})\n\t\t// \t},\n\t\tmethods: {\n\t\t\t// Refresh the order list\n\t\t\trefreshData() {\n\t\t\t\tif (this.isRefreshing) return;\n\n\t\t\t\tthis.isRefreshing = true;\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.list = [];\n\t\t\t\tthis.hasMore = true;\n\t\t\t\tthis.loadingStatus = true;\n\t\t\t\tthis.loadingText = '正在刷新...';\n\n\t\t\t\tthis.getList(false).finally(() => {\n\t\t\t\t\tthis.isRefreshing = false;\n\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// Load more orders\n\t\t\tloadMoreData() {\n\t\t\t\tif (this.isLoadingMore || !this.hasMore) return;\n\n\t\t\t\tthis.isLoadingMore = true;\n\t\t\t\tthis.loadingStatus = true;\n\t\t\t\tthis.loadingText = '正在加载更多...';\n\n\t\t\t\tthis.getList(true).finally(() => {\n\t\t\t\t\tthis.isLoadingMore = false;\n\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// Fetch order list from API\n\t\t\tgetList(append = false) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tthis.$api.shifu.masterBaolist({\n\t\t\t\t\t\tcoachId: this.userInfo.shifuId,\n\t\t\t\t\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t\tif (!res.data) {\n\t\t\t\t\t\t\tthis.total = 0;\n\t\t\t\t\t\t\tthis.list = append ? [...this.list] : [];\n\t\t\t\t\t\t\tthis.hasMore = false;\n\t\t\t\t\t\t\tthis.loadingText = '没有数据';\n\t\t\t\t\t\t\tthis.loadingStatus = true;\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Update total count\n\t\t\t\t\t\tthis.total = res.data.totalCount || 0;\n\n\t\t\t\t\t\t// Sort new data by order time (descending)\n\t\t\t\t\t\tconst newList = (res.data.list || []).sort((a, b) => b.orderTime - a.orderTime);\n\n\t\t\t\t\t\tif (append) {\n\t\t\t\t\t\t\t// Append new data to existing list\n\t\t\t\t\t\t\tthis.list = [...this.list, ...newList];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Replace list with new data\n\t\t\t\t\t\t\tthis.list = newList;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Check if more data is available\n\t\t\t\t\t\tthis.hasMore = this.list.length < this.total && newList.length === this.pageSize;\n\n\t\t\t\t\t\t// Show message if no more data\n\t\t\t\t\t\tif (!this.hasMore && this.list.length > 0) {\n\t\t\t\t\t\t\tthis.loadingText = '没有更多数据了';\n\t\t\t\t\t\t\tthis.loadingStatus = true;\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t}).catch(e => {\n\t\t\t\t\t\tconsole.error('获取列表失败:', e);\n\n\t\t\t\t\t\t// Roll back page number on load more failure\n\t\t\t\t\t\tif (append && this.page > 1) {\n\t\t\t\t\t\t\tthis.page -= 1;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Show error toast\n\t\t\t\t\t\tconst errorMsg = typeof e === 'string' ? e : e.message || '请成为师傅';\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: errorMsg\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treject(e);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// Open cancel bid modal\n\t\t\tcancelBao(item) {\n\t\t\t\tthis.showCancel = true;\n\t\t\t\tthis.id = item.orderId;\n\t\t\t},\n\n\t\t\t// Close cancel modal\n\t\t\tcancelModal() {\n\t\t\t\tthis.showCancel = false;\n\t\t\t},\n\n\t\t\t// Confirm bid cancellation\n\t\t\tconfirm() {\n\t\t\t\tthis.showCancel = false;\n\t\t\t\tthis.loadingStatus = true;\n\t\t\t\tthis.loadingText = '正在取消...';\n\n\t\t\t\tthis.$api.shifu.updateQuxiaoBao({\n\t\t\t\t\tid: this.id,\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif(res.code===\"-1\"){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\t// Refresh data\n\t\t\t\t\tthis.refreshData();\n\t\t\t\t}).catch(e => {\n\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: typeof e === 'string' ? e : e.message || '取消失败，请重试'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// Open re-bid popup\n\t\t\tagainBao(item) {\n\t\t\t\tconsole.log(item)\n\t\t\t\tthis.goodsId=item.id\n\t\t\t\tthis.show = true;\n\t\t\t\tthis.id = item.orderId;\n\t\t\t\tthis.scrollToId = 'input-box';\n\t\t\t\t// Ensure scrolling after popup renders\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.scrollToId = 'input-box';\n\t\t\t\t}, 100);\n\t\t\t},\n\n\t\t\t// Close re-bid popup\n\t\t\tclose() {\n\t\t\t\tthis.show = false;\n\t\t\t\tthis.input = '';\n\t\t\t\tthis.quoteMessage = ''; // 清空留言输入\n\t\t\t\tthis.scrollToId = '';\n\t\t\t},\n\n\t\t\t// Submit new bid\n\t\t\tconfirmBao() {\n\t\t\t\tif (this.input === '' || this.input == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入报价(不能为0哦)'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Validate input as a positive number\n\t\t\t\tconst price = parseFloat(this.input);\n\t\t\t\tif (isNaN(price) || price <= 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入有效的报价金额'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.loadingStatus = true;\n\t\t\t\tthis.loadingText = '正在提交报价...';\n\n\t\t\t\tthis.$api.shifu.updateBao({\n\t\t\t\t\torderId: this.id,\n\t\t\t\t\tprice: this.input,\n\t\t\t\t\tgoodsId:this.goodsId,\n\t\t\t\t\tquoteMessage: this.quoteMessage // 添加留言内容\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t});\n\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '报价成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.close();\n\t\t\t\t\t\t// Refresh data\n\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t}\n\t\t\t\t}).catch(e => {\n\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: typeof e === 'string' ? e : e.message || '报价失败，请重试'\n\t\t\t\t\t});\n\t\t\t\t\tthis.close();\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t// onUnload() {\n\t\t//   uni.setStorageSync('refreshReceiving', true);\n\t\t// },\n\t\tonUnload() {\n\t\t\tconsole.log('master_bao_list onUnload 触发', {\n\t\t\t\ttabId: this.tabId,\n\t\t\t\tfromQuote: this.fromQuote\n\t\t\t});\n\t\t\t// 如果是从报价页面跳转过来的，或者是报价订单tab，则需要刷新首页\n\t\t\tif(this.tabId===\"1\" || this.fromQuote){\n\t\t\t\tconsole.log('设置刷新标志');\n\t\t\t\t// 设置刷新标志\n\t\t\t\tuni.setStorageSync('needRefreshShifuIndex', true);\n\t\t\t\tconsole.log('触发全局刷新事件');\n\t\t\t\t// 使用 getApp() 来触发全局事件\n\t\t\t\tconst app = getApp();\n\t\t\t\tif (app && app.globalData) {\n\t\t\t\t\tapp.globalData.needRefreshShifuIndex = true;\n\t\t\t\t}\n\t\t\t\t// 同时触发 uni 事件\n\t\t\t\tuni.$emit('refreshReceivingList');\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t})\n\t\t\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\t// Initialize data on page load\n\t\tonLoad(option) {\n\t\t\tconsole.log('master_bao_list onLoad 参数:', option)\n\t\t\tthis.tabId=option.id\n\t\t\tthis.goodsId=option.goodsId\n\t\t\tthis.fromQuote = option.fromQuote === 'true' // 接收从报价页面跳转的标识\n\t\t\tconsole.log('设置 fromQuote:', this.fromQuote)\n\t\t\ttry {\n\t\t\t\tlet shiInfo = uni.getStorageSync('shiInfo') || '';\n\t\t\t\tif (shiInfo) {\n\t\t\t\t\tconst parsedInfo = JSON.parse(shiInfo);\n\t\t\t\t\tthis.shifuId = parsedInfo.id;\n\t\t\t\t\tconsole.log('师傅ID:', this.shifuId);\n\t\t\t\t}\n\n\t\t\t\tthis.userInfo = uni.getStorageSync('userInfo') || {};\n\t\t\t\tconsole.log('用户信息:', this.userInfo);\n\t\t\t\t\t\n\t\t\t\t// Initial data load\n\t\t\t\tthis.loadingStatus = true;\n\t\t\t\tthis.loadingText = '正在加载...';\n\t\t\t\tthis.getList().finally(() => {\n\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('初始化失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '初始化失败'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground-color: #F8F8F8;\n\t\tmin-height: 100vh;\n\n\t\t.popup-scroll {\n\t\t\tmax-height: 60vh;\n\t\t}\n\n\t\t.box {\n\t\t\tpadding: 40rpx 30rpx;\n\n\t\t\t.title {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t}\n\n\t\t\t.title2 {\n\t\t\t\tmargin-top: 32rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #171717;\n\t\t\t}\n\n\t\t\t.money {\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\n\t\t\t.message-input-section {\n\t\t\t\tmargin-bottom: 40rpx;\n\n\t\t\t\t.message-label {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #374151;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.message-input {\n\t\t\t\t\tbackground: #f8f9fa;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\tborder: 1rpx solid #e5e7eb;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tmargin: 0 auto;\n\t\t\t\tmargin-top: 42rpx;\n\t\t\t\twidth: 688rpx;\n\t\t\t\theight: 98rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tline-height: 98rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t}\n\t\t}\n\n\t\t.main {\n\t\t\tpadding: 40rpx odan30rpx;\n\n\t\t\t.main_item_already {\n\t\t\t\tpadding: 28rpx 36rpx;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t.title {\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t}\n\n\t\t\t\t.ok {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #E72427;\n\t\t\t\t}\n\n\t\t\t\t.no {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\n\t\t\t\t.mid {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.lef {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.bot {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\n\t\t\t\t.shifu {\n\t\t\t\t\tmargin-top: 20rpx;\n\n\t\t\t\t\tscroll-view {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\twhite-space: nowrap;\n\n\t\t\t\t\t\t.shifu_item {\n\t\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\t\tmargin-right: 28rpx;\n\n\t\t\t\t\t\t\t.top {\n\t\t\t\t\t\t\t\tdisplay: flex;\n\n\t\t\t\t\t\t\t\timage {\n\t\t\t\t\t\t\t\t\twidth: 92rpx;\n\t\t\t\t\t\t\t\t\theight: 92rpx;\n\t\t\t\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.info {\n\t\t\t\t\t\t\t\t\t.name {\n\t\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\tcolor: #E72427;\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.btnbox {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\tmargin-top: 52rpx;\n\n\t\t\t\t\t.btn {\n\t\t\t\t\t\twidth: 294rpx;\n\t\t\t\t\t\theight: 82rpx;\n\t\t\t\t\t\tline-height: 82rpx;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t}\n\n\t\t\t\t\t.can {\n\t\t\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\t\t}\n\n\t\t\t\t\t.re {\n\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.quote-message {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tpadding: 20rpx;\n\t\t\t\t\tbackground: #f8f9fa;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tborder-left: 4rpx solid #2E80FE;\n\n\t\t\t\t\t.message-label {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #666666;\n\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.message-content {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\t\tword-break: break-all;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Loading status styles\n\t\t\t.loading-status {\n\t\t\t\ttext-align: center;\n\t\t\t\tpadding: 40rpx 0;\n\t\t\t\tcolor: #999999;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t}\n\n\t\t\t// Empty data styles\n\t\t\t.empty-data {\n\t\t\t\ttext-align: center;\n\t\t\t\tpadding: 200rpx 0;\n\t\t\t\tcolor: #999999;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=style&index=0&id=0cdcf780&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_bao_list.vue?vue&type=style&index=0&id=0cdcf780&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756107640364\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}