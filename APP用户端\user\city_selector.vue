<template>
	<view class="page">
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity" v-if="flag"></u-picker>
		<view class="top">请选择你所在的城市以获取周边服务</view>
		<view class="main">
			<view class="main_item " @tap="goMap">
				<view class="name">服务地址</view>
				<view class="address">
					<span>{{ form.address }}</span>
				</view>
				<image src="../static/images/position.png" mode=""></image>
			</view>

			<view class="main_item">
				<view class="name">所在区域</view>
				<input type="text" v-model="form.city" placeholder="请选择所在区域" disabled @click="showCity = true">
			</view>
		</view>
		<view class="btn" @click="confirmSelection">确定</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			flag: false,
			loading: false,
			showCity: false,
			form: {
				address: '点击选择服务地址',
				city: '',
				cityId: '',
				lng: '',
				lat: ''
			},
			selectedProvince: '',
			selectedCity: '',
			selectedDistrict: '',
			columnsCity: [
				[], // Province
				[], // City
				[]  // Area
			],
		}
	},
	onLoad() {
		this.getCity(0)
		this.getNowPosition()
	},
	methods: {
		getNowPosition() {
			return new Promise((resolve) => {
				uni.getLocation({
					type: "gcj02",
					isHighAccuracy: true,
					accuracy: "best",
					success: (res) => {
						uni.setStorageSync("lat", res.latitude);
						uni.setStorageSync("lng", res.longitude);
						uni.request({
							url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,
							success: (res1) => {
								console.log(res1)
								this.form.address = res1.data.regeocode.formatted_address
								// Store coordinates
								this.form.lng = res.longitude;
								this.form.lat = res.latitude;
								resolve();
							},
							fail: (err) => {
								console.error("逆地理编码失败:", err);
								resolve();
							}
						});
					},
					fail: (err) => {
						console.error("获取定位失败:", err);
						resolve();
					}
				});
			});
		},

		goMap() {
			let that = this
			// #ifdef MP-WEIXIN
			uni.authorize({
				scope: 'scope.userLocation',
				success(res) {
					setTimeout(() => {
						uni.chooseLocation({
							success: function (res) {
								console.log('选择位置成功:', res);
								try {
									that.form.address = res.name || '未知位置'
									that.form.lng = res.longitude || ''
									that.form.lat = res.latitude || ''

									uni.showToast({
										title: '位置选择成功',
										icon: 'success',
										duration: 1500
									});
								} catch (error) {
									console.error('处理位置信息时出错:', error);
									uni.showToast({
										title: '位置信息处理失败',
										icon: 'none',
										duration: 2000
									});
								}
							},
							fail: function (err) {
								console.error('选择位置失败:', err);
								uni.showToast({
									title: '选择位置失败，请重试',
									icon: 'none',
									duration: 2000
								});
							}
						});
					}, 300);
				},
				fail(err) {
					console.error('位置授权失败:', err)
					uni.showToast({
						title: '请授权位置信息',
						icon: 'none',
						duration: 2000
					});
				}
			})
			// #endif
			// #ifdef APP
			setTimeout(() => {
				try {
					uni.chooseLocation({
						success: function (res) {
							console.log('APP选择位置成功:', res)
							try {
								that.form.address = (res.name && typeof res.name === 'string') ? res.name : '选择的位置'
								that.form.lng = (res.longitude && typeof res.longitude === 'number') ? res.longitude.toString() : ''
								that.form.lat = (res.latitude && typeof res.latitude === 'number') ? res.latitude.toString() : ''

								uni.showToast({
									title: '位置选择成功',
									icon: 'success',
									duration: 1500
								});
							} catch (error) {
								console.error('APP处理位置信息时出错:', error);
								that.form.address = '位置信息'
								that.form.lng = ''
								that.form.lat = ''

								uni.showToast({
									title: '位置信息处理失败，请重新选择',
									icon: 'none',
									duration: 2500
								});
							}
						},
						fail: function (err) {
							console.error('APP选择位置失败:', err);
							uni.showToast({
								title: '选择位置失败，请重试',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} catch (globalError) {
					console.error('APP端chooseLocation调用失败:', globalError);
					uni.showToast({
						title: '地图功能暂时不可用',
						icon: 'none',
						duration: 2000
					});
				}
			}, 500);
			// #endif
		},

		confirmCity(Array) {
			// 构建完整的省市区信息
			const selectedItems = Array.value.map((item, index) => {
				if (item == undefined) {
					return this.columnsCity[index][0] || {};
				} else {
					return item;
				}
			});

			// 保存完整的省市区信息
			this.selectedProvince = selectedItems[0]?.title || '';
			this.selectedCity = selectedItems[1]?.title || '';
			this.selectedDistrict = selectedItems[2]?.title || '';

			// 构建显示文本：省,市,区 (参考 master_Info.vue)
			this.form.city = selectedItems
				.map(item => item.title || '')
				.filter(title => title)
				.join(',');

			// 构建ID数组，传参时使用最后一级（区县）的ID
			const cityIds = selectedItems.map(item => item.id || 0);

			// 根据需求，传参是传ID，这里保存最后一级的ID（区县ID）
			this.form.cityId = cityIds[cityIds.length - 1] || cityIds[0];

			this.showCity = false;

			// Get city info from API to display trueName
			if (this.form.cityId) {
				this.getCityInfo(this.form.cityId);
			}
		},

		// Get city information from API
		async getCityInfo(cityId) {
			try {
				const res = await uni.request({
					url: `/api/core/city/${cityId}`,
					method: 'GET'
				});

				if (res.data && res.data.code === '200') {
					const cityData = res.data.data;
					console.log('City info:', cityData);

					// 如果有区县信息，优先显示区县的trueName
					if (this.selectedDistrict) {
						// 显示最后一级（区县）的名称
						this.form.city = cityData.trueName || this.selectedDistrict;
					} else if (this.selectedCity) {
						// 如果没有区县，显示城市名称
						this.form.city = cityData.trueName || this.selectedCity;
					} else {
						// 最后显示省份名称
						this.form.city = cityData.trueName || this.selectedProvince;
					}
				}
			} catch (error) {
				console.error('获取城市信息失败:', error);
				// API失败时，使用本地选择的最后一级名称
				if (this.selectedDistrict) {
					this.form.city = this.selectedDistrict;
				} else if (this.selectedCity) {
					this.form.city = this.selectedCity;
				} else {
					this.form.city = this.selectedProvince;
				}
			}
		},

		getCity(e) {
			this.$api.service.getCity(e).then(res => {
				console.log(res)
				// 检查数据结构，兼容不同的返回格式
				const data = res.data || res;
				if (Array.isArray(data)) {
					// 转换数据格式，将trueName转换为title以适配picker组件 (参考 master_Info.vue)
					const provinces = data.map(item => ({
						...item,
						title: item.trueName || item.title
					}));
					this.columnsCity[0] = provinces;

					// 初始化第一个省份的城市数据
					if (provinces.length > 0 && provinces[0].children) {
						const cities = provinces[0].children.map(item => ({
							...item,
							title: item.trueName || item.title
						}));
						this.columnsCity[1] = cities;

						// 初始化第一个城市的区县数据
						if (cities.length > 0 && cities[0].children) {
							const districts = cities[0].children.map(item => ({
								...item,
								title: item.trueName || item.title
							}));
							this.columnsCity[2] = districts;
						} else {
							this.columnsCity[2] = [];
						}
					} else {
						this.columnsCity[1] = [];
						this.columnsCity[2] = [];
					}
				} else {
					// 如果不是数组，可能是旧的API格式
					this.columnsCity[0] = res
					if (res[0]?.id) {
						this.$api.service.getCity(res[0].id).then(res1 => {
							this.columnsCity[1] = res1
							if (res1[0]?.id) {
								this.$api.service.getCity(res1[0].id).then(res2 => {
									this.columnsCity[2] = res2
								})
							}
						})
					}
				}
				this.flag = true;
			}).catch(err => {
				console.error('Failed to fetch city data:', err)
			})
		},

		changeHandler(e) {
			const { columnIndex, index, picker = this.$refs.uPicker } = e;
			if (columnIndex === 0) {
				// 选择省份时，从children中获取对应的城市列表 (参考 master_Info.vue)
				const selectedProvince = this.columnsCity[0][index];
				if (selectedProvince && selectedProvince.children) {
					const cities = selectedProvince.children.map(item => ({
						...item,
						title: item.trueName || item.title
					}));
					picker.setColumnValues(1, cities);
					this.columnsCity[1] = cities;

					// 同时更新第一个城市的区县数据
					if (cities.length > 0 && cities[0].children) {
						const districts = cities[0].children.map(item => ({
							...item,
							title: item.trueName || item.title
						}));
						picker.setColumnValues(2, districts);
						this.columnsCity[2] = districts;
					} else {
						picker.setColumnValues(2, []);
						this.columnsCity[2] = [];
					}
				} else {
					picker.setColumnValues(1, []);
					picker.setColumnValues(2, []);
					this.columnsCity[1] = [];
					this.columnsCity[2] = [];
				}
			} else if (columnIndex === 1) {
				// 选择城市时，从children中获取对应的区县列表 (参考 master_Info.vue)
				const selectedCity = this.columnsCity[1][index];
				if (selectedCity && selectedCity.children) {
					const districts = selectedCity.children.map(item => ({
						...item,
						title: item.trueName || item.title
					}));
					picker.setColumnValues(2, districts);
					this.columnsCity[2] = districts;
				} else {
					picker.setColumnValues(2, []);
					this.columnsCity[2] = [];
				}
			}
		},

		confirmSelection() {
			if (!this.form.city) {
				uni.showToast({
					icon: 'none',
					title: '请选择所在区域',
					duration: 1500
				})
				return
			}

			// Store selected city info (temporary for page navigation)
			uni.setStorageSync('selectedCity', {
				city: this.form.city, // 这里是最后一级的名称（如：临泉县）
				cityId: this.form.cityId,
				address: this.form.address,
				lng: this.form.lng,
				lat: this.form.lat,
				fullPath: `${this.selectedProvince},${this.selectedCity},${this.selectedDistrict}`.replace(/^,|,$/g, ''), // 完整路径
				province: this.selectedProvince,
				cityName: this.selectedCity,
				district: this.selectedDistrict
			});

			uni.showToast({
				title: '选择成功',
				icon: 'success',
				duration: 1500
			});

			// Navigate back and update service page
			uni.navigateBack({
				success: () => {
					// Emit event to update service page
					uni.$emit('citySelected', {
						city: this.form.city,
						cityId: this.form.cityId
					});
				}
			});
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	height: 100vh;
	background-color: #fff;

	.top {
		width: 750rpx;
		height: 58rpx;
		background: #FFF7F1;
		font-size: 28rpx;
		font-weight: 400;
		color: #FE921B;
		line-height: 58rpx;
		text-align: center;
	}

	.btn {
		margin: 0 auto;
		margin-top: 88rpx;
		width: 690rpx;
		height: 98rpx;
		background: #2E80FE;
		border-radius: 50rpx 50rpx 50rpx 50rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 98rpx;
		text-align: center;
	}

	.main {
		padding: 0 30rpx;

		.main_item {
			padding: 40rpx 0;
			border-bottom: 2rpx solid #E9E9E9;
			display: flex;
			align-items: center;
			position: relative;

			.name {
				min-width: 112rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
				margin-right: 40rpx;
			}

			.address {
				font-size: 28rpx;
				font-weight: 400;
				color: #ADADAD;
			}

			image {
				width: 23rpx;
				height: 27rpx;
				position: absolute;
				right: 0;
				top: 46rpx;
			}

			input {
				width: 450rpx;
			}
		}
	}
}
</style>
