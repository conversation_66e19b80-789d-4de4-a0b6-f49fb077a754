@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-b81bd106 {
  height: 100vh;
  background-color: #fff;
}
.page .top.data-v-b81bd106 {
  width: 750rpx;
  height: 58rpx;
  background: #FFF7F1;
  font-size: 28rpx;
  font-weight: 400;
  color: #FE921B;
  line-height: 58rpx;
  text-align: center;
}
.page .btn.data-v-b81bd106 {
  margin: 0 auto;
  margin-top: 88rpx;
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}
.page .main.data-v-b81bd106 {
  padding: 0 30rpx;
}
.page .main .main_item.data-v-b81bd106 {
  padding: 40rpx 0;
  border-bottom: 2rpx solid #E9E9E9;
  display: flex;
  align-items: center;
  position: relative;
}
.page .main .main_item .name.data-v-b81bd106 {
  min-width: 112rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-right: 40rpx;
}
.page .main .main_item .address.data-v-b81bd106 {
  font-size: 28rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .main .main_item image.data-v-b81bd106 {
  width: 23rpx;
  height: 27rpx;
  position: absolute;
  right: 0;
  top: 46rpx;
}
.page .main .main_item input.data-v-b81bd106 {
  width: 450rpx;
}

