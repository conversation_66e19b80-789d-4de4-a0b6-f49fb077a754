{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/city_selector.vue?d7c3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/city_selector.vue?e101", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/city_selector.vue?35eb", "uni-app:///user/city_selector.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/city_selector.vue?1ffb", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/city_selector.vue?6133"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "flag", "loading", "showCity", "form", "address", "city", "cityId", "lng", "lat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCity", "selectedDistrict", "columnsCity", "onLoad", "methods", "getNowPosition", "uni", "type", "isHighAccuracy", "accuracy", "success", "url", "console", "resolve", "fail", "goMap", "scope", "setTimeout", "that", "title", "icon", "duration", "confirmCity", "map", "filter", "join", "getCityInfo", "method", "res", "cityData", "getCity", "item", "<PERSON><PERSON><PERSON><PERSON>", "index", "picker", "confirmSelection", "fullPath", "province", "cityName", "district"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwBh3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC,cACA;MAAA;MACA;MAAA;MACA;MAAA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;UACAC;UACAC;UACAC;UACAC;YACAJ;YACAA;YACAA;cACAK;cACAD;gBACAE;gBACA;gBACA;gBACA;gBACA;gBACAC;cACA;cACAC;gBACAF;gBACAC;cACA;YACA;UACA;UACAC;YACAF;YACAC;UACA;QACA;MACA;IACA;IAEAE;MACA;MAEAT;QACAU;QACAN;UACAO;YACAX;cACAI;gBACAE;gBACA;kBACAM;kBACAA;kBACAA;kBAEAZ;oBACAa;oBACAC;oBACAC;kBACA;gBACA;kBACAT;kBACAN;oBACAa;oBACAC;oBACAC;kBACA;gBACA;cACA;cACAP;gBACAF;gBACAN;kBACAa;kBACAC;kBACAC;gBACA;cACA;YACA;UACA;QACA;QACAP;UACAF;UACAN;YACAa;YACAC;YACAC;UACA;QACA;MACA;IAkDA;IAEAC;MAAA;QAAA;QAAA;QAAA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA,+BACAC;QAAA;MAAA,GACAC;QAAA;MAAA,GACAC;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MAEA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEApB;kBACAK;kBACAgB;gBACA;cAAA;gBAHAC;gBAKA;kBACAC;kBACAjB;;kBAEA;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAkB;MAAA;MACA;QACAlB;QACA;QACA;QACA;UACA;UACA;YAAA,uCACAmB;cACAZ;YAAA;UAAA,CACA;UACA;;UAEA;UACA;YACA;cAAA,uCACAY;gBACAZ;cAAA;YAAA,CACA;YACA;;YAEA;YACA;cACA;gBAAA,uCACAY;kBACAZ;gBAAA;cAAA,CACA;cACA;YACA;cACA;YACA;UACA;YACA;YACA;UACA;QACA;UAAA;UACA;UACA;UACA;YACA;cAAA;cACA;cACA;gBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;QACA;MACA;QACAP;MACA;IACA;IAEAoB;MACA;QAAAC;QAAA;QAAAC;MACA;QACA;QACA;QACA;UACA;YAAA,uCACAH;cACAZ;YAAA;UAAA,CACA;UACAe;UACA;;UAEA;UACA;YACA;cAAA,uCACAH;gBACAZ;cAAA;YAAA,CACA;YACAe;YACA;UACA;YACAA;YACA;UACA;QACA;UACAA;UACAA;UACA;UACA;QACA;MACA;QACA;QACA;QACA;UACA;YAAA,uCACAH;cACAZ;YAAA;UAAA,CACA;UACAe;UACA;QACA;UACAA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;QACA7B;UACAc;UACAD;UACAE;QACA;QACA;MACA;;MAEA;MACAf;QACAX;QAAA;QACAC;QACAF;QACAG;QACAC;QACAsC;QAAA;QACAC;QACAC;QACAC;MACA;MAEAjC;QACAa;QACAC;QACAC;MACA;;MAEA;MACAf;QACAI;UACA;UACAJ;YACAX;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjZA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/city_selector.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/city_selector.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./city_selector.vue?vue&type=template&id=b81bd106&scoped=true&\"\nvar renderjs\nimport script from \"./city_selector.vue?vue&type=script&lang=js&\"\nexport * from \"./city_selector.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city_selector.vue?vue&type=style&index=0&id=b81bd106&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b81bd106\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/city_selector.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city_selector.vue?vue&type=template&id=b81bd106&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCity = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city_selector.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city_selector.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-picker :show=\"showCity\" ref=\"uPicker\" :loading=\"loading\" :columns=\"columnsCity\" @change=\"changeHandler\"\n\t\t\tkeyName=\"title\" @cancel=\"showCity = false\" @confirm=\"confirmCity\" v-if=\"flag\"></u-picker>\n\t\t<view class=\"top\">请选择你所在的城市以获取周边服务</view>\n\t\t<view class=\"main\">\n\t\t\t<view class=\"main_item \" @tap=\"goMap\">\n\t\t\t\t<view class=\"name\">服务地址</view>\n\t\t\t\t<view class=\"address\">\n\t\t\t\t\t<span>{{ form.address }}</span>\n\t\t\t\t</view>\n\t\t\t\t<image src=\"../static/images/position.png\" mode=\"\"></image>\n\t\t\t</view>\n\n\t\t\t<view class=\"main_item\">\n\t\t\t\t<view class=\"name\">所在区域</view>\n\t\t\t\t<input type=\"text\" v-model=\"form.city\" placeholder=\"请选择所在区域\" disabled @click=\"showCity = true\">\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"btn\" @click=\"confirmSelection\">确定</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tflag: false,\n\t\t\tloading: false,\n\t\t\tshowCity: false,\n\t\t\tform: {\n\t\t\t\taddress: '点击选择服务地址',\n\t\t\t\tcity: '',\n\t\t\t\tcityId: '',\n\t\t\t\tlng: '',\n\t\t\t\tlat: ''\n\t\t\t},\n\t\t\tselectedProvince: '',\n\t\t\tselectedCity: '',\n\t\t\tselectedDistrict: '',\n\t\t\tcolumnsCity: [\n\t\t\t\t[], // Province\n\t\t\t\t[], // City\n\t\t\t\t[]  // Area\n\t\t\t],\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.getCity(0)\n\t\tthis.getNowPosition()\n\t},\n\tmethods: {\n\t\tgetNowPosition() {\n\t\t\treturn new Promise((resolve) => {\n\t\t\t\tuni.getLocation({\n\t\t\t\t\ttype: \"gcj02\",\n\t\t\t\t\tisHighAccuracy: true,\n\t\t\t\t\taccuracy: \"best\",\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tuni.setStorageSync(\"lat\", res.latitude);\n\t\t\t\t\t\tuni.setStorageSync(\"lng\", res.longitude);\n\t\t\t\t\t\tuni.request({\n\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,\n\t\t\t\t\t\t\tsuccess: (res1) => {\n\t\t\t\t\t\t\t\tconsole.log(res1)\n\t\t\t\t\t\t\t\tthis.form.address = res1.data.regeocode.formatted_address\n\t\t\t\t\t\t\t\t// Store coordinates\n\t\t\t\t\t\t\t\tthis.form.lng = res.longitude;\n\t\t\t\t\t\t\t\tthis.form.lat = res.latitude;\n\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.error(\"逆地理编码失败:\", err);\n\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error(\"获取定位失败:\", err);\n\t\t\t\t\t\tresolve();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\n\t\tgoMap() {\n\t\t\tlet that = this\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tuni.authorize({\n\t\t\t\tscope: 'scope.userLocation',\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.chooseLocation({\n\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\tconsole.log('选择位置成功:', res);\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tthat.form.address = res.name || '未知位置'\n\t\t\t\t\t\t\t\t\tthat.form.lng = res.longitude || ''\n\t\t\t\t\t\t\t\t\tthat.form.lat = res.latitude || ''\n\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '位置选择成功',\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\tconsole.error('处理位置信息时出错:', error);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '位置信息处理失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function (err) {\n\t\t\t\t\t\t\t\tconsole.error('选择位置失败:', err);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '选择位置失败，请重试',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 300);\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.error('位置授权失败:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请授权位置信息',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t})\n\t\t\t// #endif\n\t\t\t// #ifdef APP\n\t\t\tsetTimeout(() => {\n\t\t\t\ttry {\n\t\t\t\t\tuni.chooseLocation({\n\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\tconsole.log('APP选择位置成功:', res)\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tthat.form.address = (res.name && typeof res.name === 'string') ? res.name : '选择的位置'\n\t\t\t\t\t\t\t\tthat.form.lng = (res.longitude && typeof res.longitude === 'number') ? res.longitude.toString() : ''\n\t\t\t\t\t\t\t\tthat.form.lat = (res.latitude && typeof res.latitude === 'number') ? res.latitude.toString() : ''\n\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '位置选择成功',\n\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\tconsole.error('APP处理位置信息时出错:', error);\n\t\t\t\t\t\t\t\tthat.form.address = '位置信息'\n\t\t\t\t\t\t\t\tthat.form.lng = ''\n\t\t\t\t\t\t\t\tthat.form.lat = ''\n\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '位置信息处理失败，请重新选择',\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 2500\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: function (err) {\n\t\t\t\t\t\t\tconsole.error('APP选择位置失败:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '选择位置失败，请重试',\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (globalError) {\n\t\t\t\t\tconsole.error('APP端chooseLocation调用失败:', globalError);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '地图功能暂时不可用',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 500);\n\t\t\t// #endif\n\t\t},\n\n\t\tconfirmCity(Array) {\n\t\t\t// 构建完整的省市区信息\n\t\t\tconst selectedItems = Array.value.map((item, index) => {\n\t\t\t\tif (item == undefined) {\n\t\t\t\t\treturn this.columnsCity[index][0] || {};\n\t\t\t\t} else {\n\t\t\t\t\treturn item;\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// 保存完整的省市区信息\n\t\t\tthis.selectedProvince = selectedItems[0]?.title || '';\n\t\t\tthis.selectedCity = selectedItems[1]?.title || '';\n\t\t\tthis.selectedDistrict = selectedItems[2]?.title || '';\n\n\t\t\t// 构建显示文本：省,市,区 (参考 master_Info.vue)\n\t\t\tthis.form.city = selectedItems\n\t\t\t\t.map(item => item.title || '')\n\t\t\t\t.filter(title => title)\n\t\t\t\t.join(',');\n\n\t\t\t// 构建ID数组，传参时使用最后一级（区县）的ID\n\t\t\tconst cityIds = selectedItems.map(item => item.id || 0);\n\n\t\t\t// 根据需求，传参是传ID，这里保存最后一级的ID（区县ID）\n\t\t\tthis.form.cityId = cityIds[cityIds.length - 1] || cityIds[0];\n\n\t\t\tthis.showCity = false;\n\n\t\t\t// Get city info from API to display trueName\n\t\t\tif (this.form.cityId) {\n\t\t\t\tthis.getCityInfo(this.form.cityId);\n\t\t\t}\n\t\t},\n\n\t\t// Get city information from API\n\t\tasync getCityInfo(cityId) {\n\t\t\ttry {\n\t\t\t\tconst res = await uni.request({\n\t\t\t\t\turl: `/api/core/city/${cityId}`,\n\t\t\t\t\tmethod: 'GET'\n\t\t\t\t});\n\n\t\t\t\tif (res.data && res.data.code === '200') {\n\t\t\t\t\tconst cityData = res.data.data;\n\t\t\t\t\tconsole.log('City info:', cityData);\n\n\t\t\t\t\t// 如果有区县信息，优先显示区县的trueName\n\t\t\t\t\tif (this.selectedDistrict) {\n\t\t\t\t\t\t// 显示最后一级（区县）的名称\n\t\t\t\t\t\tthis.form.city = cityData.trueName || this.selectedDistrict;\n\t\t\t\t\t} else if (this.selectedCity) {\n\t\t\t\t\t\t// 如果没有区县，显示城市名称\n\t\t\t\t\t\tthis.form.city = cityData.trueName || this.selectedCity;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 最后显示省份名称\n\t\t\t\t\t\tthis.form.city = cityData.trueName || this.selectedProvince;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取城市信息失败:', error);\n\t\t\t\t// API失败时，使用本地选择的最后一级名称\n\t\t\t\tif (this.selectedDistrict) {\n\t\t\t\t\tthis.form.city = this.selectedDistrict;\n\t\t\t\t} else if (this.selectedCity) {\n\t\t\t\t\tthis.form.city = this.selectedCity;\n\t\t\t\t} else {\n\t\t\t\t\tthis.form.city = this.selectedProvince;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tgetCity(e) {\n\t\t\tthis.$api.service.getCity(e).then(res => {\n\t\t\t\tconsole.log(res)\n\t\t\t\t// 检查数据结构，兼容不同的返回格式\n\t\t\t\tconst data = res.data || res;\n\t\t\t\tif (Array.isArray(data)) {\n\t\t\t\t\t// 转换数据格式，将trueName转换为title以适配picker组件 (参考 master_Info.vue)\n\t\t\t\t\tconst provinces = data.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\ttitle: item.trueName || item.title\n\t\t\t\t\t}));\n\t\t\t\t\tthis.columnsCity[0] = provinces;\n\n\t\t\t\t\t// 初始化第一个省份的城市数据\n\t\t\t\t\tif (provinces.length > 0 && provinces[0].children) {\n\t\t\t\t\t\tconst cities = provinces[0].children.map(item => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\ttitle: item.trueName || item.title\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tthis.columnsCity[1] = cities;\n\n\t\t\t\t\t\t// 初始化第一个城市的区县数据\n\t\t\t\t\t\tif (cities.length > 0 && cities[0].children) {\n\t\t\t\t\t\t\tconst districts = cities[0].children.map(item => ({\n\t\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\t\ttitle: item.trueName || item.title\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.columnsCity[2] = [];\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.columnsCity[1] = [];\n\t\t\t\t\t\tthis.columnsCity[2] = [];\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 如果不是数组，可能是旧的API格式\n\t\t\t\t\tthis.columnsCity[0] = res\n\t\t\t\t\tif (res[0]?.id) {\n\t\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\n\t\t\t\t\t\t\tthis.columnsCity[1] = res1\n\t\t\t\t\t\t\tif (res1[0]?.id) {\n\t\t\t\t\t\t\t\tthis.$api.service.getCity(res1[0].id).then(res2 => {\n\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = res2\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.flag = true;\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('Failed to fetch city data:', err)\n\t\t\t})\n\t\t},\n\n\t\tchangeHandler(e) {\n\t\t\tconst { columnIndex, index, picker = this.$refs.uPicker } = e;\n\t\t\tif (columnIndex === 0) {\n\t\t\t\t// 选择省份时，从children中获取对应的城市列表 (参考 master_Info.vue)\n\t\t\t\tconst selectedProvince = this.columnsCity[0][index];\n\t\t\t\tif (selectedProvince && selectedProvince.children) {\n\t\t\t\t\tconst cities = selectedProvince.children.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\ttitle: item.trueName || item.title\n\t\t\t\t\t}));\n\t\t\t\t\tpicker.setColumnValues(1, cities);\n\t\t\t\t\tthis.columnsCity[1] = cities;\n\n\t\t\t\t\t// 同时更新第一个城市的区县数据\n\t\t\t\t\tif (cities.length > 0 && cities[0].children) {\n\t\t\t\t\t\tconst districts = cities[0].children.map(item => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\ttitle: item.trueName || item.title\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tpicker.setColumnValues(2, districts);\n\t\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tpicker.setColumnValues(2, []);\n\t\t\t\t\t\tthis.columnsCity[2] = [];\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tpicker.setColumnValues(1, []);\n\t\t\t\t\tpicker.setColumnValues(2, []);\n\t\t\t\t\tthis.columnsCity[1] = [];\n\t\t\t\t\tthis.columnsCity[2] = [];\n\t\t\t\t}\n\t\t\t} else if (columnIndex === 1) {\n\t\t\t\t// 选择城市时，从children中获取对应的区县列表 (参考 master_Info.vue)\n\t\t\t\tconst selectedCity = this.columnsCity[1][index];\n\t\t\t\tif (selectedCity && selectedCity.children) {\n\t\t\t\t\tconst districts = selectedCity.children.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\ttitle: item.trueName || item.title\n\t\t\t\t\t}));\n\t\t\t\t\tpicker.setColumnValues(2, districts);\n\t\t\t\t\tthis.columnsCity[2] = districts;\n\t\t\t\t} else {\n\t\t\t\t\tpicker.setColumnValues(2, []);\n\t\t\t\t\tthis.columnsCity[2] = [];\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tconfirmSelection() {\n\t\t\tif (!this.form.city) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '请选择所在区域',\n\t\t\t\t\tduration: 1500\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\t// Store selected city info (temporary for page navigation)\n\t\t\tuni.setStorageSync('selectedCity', {\n\t\t\t\tcity: this.form.city, // 这里是最后一级的名称（如：临泉县）\n\t\t\t\tcityId: this.form.cityId,\n\t\t\t\taddress: this.form.address,\n\t\t\t\tlng: this.form.lng,\n\t\t\t\tlat: this.form.lat,\n\t\t\t\tfullPath: `${this.selectedProvince},${this.selectedCity},${this.selectedDistrict}`.replace(/^,|,$/g, ''), // 完整路径\n\t\t\t\tprovince: this.selectedProvince,\n\t\t\t\tcityName: this.selectedCity,\n\t\t\t\tdistrict: this.selectedDistrict\n\t\t\t});\n\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '选择成功',\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: 1500\n\t\t\t});\n\n\t\t\t// Navigate back and update service page\n\t\t\tuni.navigateBack({\n\t\t\t\tsuccess: () => {\n\t\t\t\t\t// Emit event to update service page\n\t\t\t\t\tuni.$emit('citySelected', {\n\t\t\t\t\t\tcity: this.form.city,\n\t\t\t\t\t\tcityId: this.form.cityId\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\theight: 100vh;\n\tbackground-color: #fff;\n\n\t.top {\n\t\twidth: 750rpx;\n\t\theight: 58rpx;\n\t\tbackground: #FFF7F1;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #FE921B;\n\t\tline-height: 58rpx;\n\t\ttext-align: center;\n\t}\n\n\t.btn {\n\t\tmargin: 0 auto;\n\t\tmargin-top: 88rpx;\n\t\twidth: 690rpx;\n\t\theight: 98rpx;\n\t\tbackground: #2E80FE;\n\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #FFFFFF;\n\t\tline-height: 98rpx;\n\t\ttext-align: center;\n\t}\n\n\t.main {\n\t\tpadding: 0 30rpx;\n\n\t\t.main_item {\n\t\t\tpadding: 40rpx 0;\n\t\t\tborder-bottom: 2rpx solid #E9E9E9;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tposition: relative;\n\n\t\t\t.name {\n\t\t\t\tmin-width: 112rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #333333;\n\t\t\t\tmargin-right: 40rpx;\n\t\t\t}\n\n\t\t\t.address {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #ADADAD;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 23rpx;\n\t\t\t\theight: 27rpx;\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 0;\n\t\t\t\ttop: 46rpx;\n\t\t\t}\n\n\t\t\tinput {\n\t\t\t\twidth: 450rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city_selector.vue?vue&type=style&index=0&id=b81bd106&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city_selector.vue?vue&type=style&index=0&id=b81bd106&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756107595022\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}