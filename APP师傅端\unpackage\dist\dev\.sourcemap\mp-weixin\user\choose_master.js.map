{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/choose_master.vue?a2c2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/choose_master.vue?ec34", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/choose_master.vue?0e6f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/choose_master.vue?75cb", "uni-app:///user/choose_master.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/choose_master.vue?17da", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/choose_master.vue?e1b9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "show", "<PERSON><PERSON><PERSON><PERSON>", "content", "info", "showCancel", "que_id", "selectPrice", "methods", "getcommissionRatio", "console", "chooseOne", "confirmMaster", "orderPrice", "quotedPriceId", "uni", "icon", "title", "setTimeout", "url", "cancelO", "confirmCancel", "id", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0Ch3B;EACAC;IACA;MACAC;MAAAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACA;QACAA;MACA;IACA;IACAC;MACAD;MACA;MACA;MAEA;IACA;IACAE;MACA;MACAF;MACA;QACAG;QACAC;MACA;QACAJ;QACAK;UACAC;UACAC;QACA;QACAC;UACA;UACAH;UACAA;YACAI;UACA;QACA;MACA;QACAT;QACAK;UACAC;UACAC;QACA;MAEA;IACA;IACAG;MACA;IACA;IACAC;MACA;MACAX;MACA;QACAY;MACA;QACAP;UACAC;UACAC;QACA;QACAP;QACAK;QACAA;MACA;QACAL;QACAK;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAM;IACA;IACAb;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/choose_master.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/choose_master.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./choose_master.vue?vue&type=template&id=105ecd58&scoped=true&\"\nvar renderjs\nimport script from \"./choose_master.vue?vue&type=script&lang=js&\"\nexport * from \"./choose_master.vue?vue&type=script&lang=js&\"\nimport style0 from \"./choose_master.vue?vue&type=style&index=0&id=105ecd58&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"105ecd58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/choose_master.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_master.vue?vue&type=template&id=105ecd58&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.info.quotedPriceVos, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.$util.timestampToTime(item.priceCreateTime * 1000)\n    var g1 = (item.price * (1 + _vm.jiaNum / 100)).toFixed(2)\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCancel = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_master.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_master.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<u-modal :show=\"show\" :content='content' :showCancelButton=\"true\" cancelText=\"再想想\" @cancel=\"show= false\"\n\t\t\t@confirm=\"confirmMaster\"></u-modal>\n\t\t<view class=\"header\">\n\t\t\t<view class=\"title\">等待您选择师傅</view>\n\t\t\t<view class=\"desc\">师傅可能在服务，请耐心等待</view>\n\t\t\t<view class=\"time\">距师傅报价截止还剩：<u-count-down :time=\"24 * 60 * 60 * 1000\" format=\"HH:mm:ss\"></u-count-down>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"main\">\n\t\t\t<scroll-view scroll-y=\"true\">\n\t\t\t\t<view class=\"main_item\" v-for=\"(item,index) in info.quotedPriceVos\" :key=\"index\">\n\t\t\t\t\t<view class=\"time\">报价时间{{$util.timestampToTime(item.priceCreateTime*1000)}}</view>\n\t\t\t\t\t<view class=\"box\">\n\t\t\t\t\t\t<image :src=\"item.selfImg?item.selfImg:'/static/mine/default_user.png'\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t\t\t<view class=\"name\">{{item.coachName}}</view>\n\t\t\t\t\t\t\t\t<view class=\"level\" v-if=\"item.labelName != null\">{{item.labelName}}</view>\n\t\t\t\t\t\t\t\t<view class=\"promise\" v-if=\"item.cashPledge != null\">已缴纳保证金</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom\">服务<span>{{item.count}}</span>次</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"price\">￥{{(item.price * (1 + jiaNum / 100)).toFixed(2) }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"down\">\n\t\t\t\t\t\t<view class=\"btn\" @click=\"chooseOne(item)\">选择他</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn\" @click=\"cancelO\">取消订单</view>\n\t\t</view>\n\n\t\t<u-modal :show=\"showCancel\" title=\"取消订单\" content='确认要取消该订单吗' showCancelButton @cancel=\"showCancel = false\"\n\t\t\t@confirm=\"confirmCancel\"></u-modal>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshow: false,jiaNum:0,\n\t\t\t\tcontent: '确认选择这个师傅的报价吗？',\n\t\t\t\tinfo: {},\n\t\t\t\tshowCancel: false,\n\t\t\t\tque_id: '',\n\t\t\t\tselectPrice: '',\n\t\t\t}\n\t\t},\n\t\tmethods: {\r\n\t\t\tgetcommissionRatio(){\r\n\t\t\t\tthis.$api.service.commissionRatio().then(res=>{\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthis.jiaNum=res\r\n\t\t\t\t\tconsole.log(this.jiaNum)\r\n\t\t\t\t})\r\n\t\t\t},\n\t\t\tchooseOne(item) {\n\t\t\t\tconsole.log('Selected item:', item)\n\t\t\t\tthis.show = true\n\t\t\t\tthis.que_id = item.id\r\n\t\t\t\t\n\t\t\t\tthis.selectPrice =  parseFloat((item.price * (1 + this.jiaNum / 100)).toFixed(2));\n\t\t\t},\n\t\t\tconfirmMaster() {\n\t\t\t\tthis.show = false\n\t\t\t\tconsole.log('Confirming master with price:', this.selectPrice, 'and ID:', this.que_id)\n\t\t\t\tthis.$api.service.choosePrice({\n\t\t\t\t\torderPrice: this.selectPrice,\n\t\t\t\t\tquotedPriceId: this.que_id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log('API response:', res)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '选择成功,请完成支付'\n\t\t\t\t\t})\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t// console.log('Emitting cancelOr event')\n\t\t\t\t\t\tuni.$emit('cancelOr')\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl:'/user/order_list?tab=1'\n\t\t\t\t\t\t})\n\t\t\t\t\t}, 2000)\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('API error:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '选择失败'\n\t\t\t\t\t})\r\n\t\t\t\t\t\n\t\t\t\t})\n\t\t\t},\n\t\t\tcancelO() {\n\t\t\t\tthis.showCancel = true\n\t\t\t},\n\t\t\tconfirmCancel() {\n\t\t\t\tthis.showCancel = false\n\t\t\t\tconsole.log('Cancelling order with ID:', this.info.id)\n\t\t\t\tthis.$api.service.cancelOrder({\n\t\t\t\t\tid: this.info.id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t\t})\n\t\t\t\t\tconsole.log('Emitting cancelOr event for cancel')\n\t\t\t\t\tuni.$emit('cancelOr')\n\t\t\t\t\tuni.navigateBack()\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('Cancel error:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '取消失败'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.info = this.$store.state.service.orderInfo\n\t\t\tconsole.log('Loaded order info:', this.info)\r\n\t\t\tthis.getcommissionRatio()\n\t\t}\n\t}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground: #f3f4f5;\n\t\theight: 100vh;\n\n\t\t.header {\n\t\t\tpadding: 40rpx 32rpx;\n\n\t\t\t.title {\n\t\t\t\tfont-size: 40rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\n\t\t\t.desc {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #ADADAD;\n\t\t\t}\n\n\t\t\t.time {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #ADADAD;\n\n\t\t\t\t::v-deep .u-count-down__text {\n\t\t\t\t\tcolor: #E72427;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.main {\n\t\t\twidth: 750rpx;\n\t\t\theight: 82vh;\n\t\t\tpadding: 0 32rpx;\n\t\t\tbackground: #ffffff;\n\t\t\tborder-radius: 40rpx 40rpx 0rpx 0rpx;\n\t\t\tpadding-bottom: 192rpx;\n\n\t\t\tscroll-view {\n\t\t\t\theight: 100%;\n\n\t\t\t\t.main_item {\n\t\t\t\t\tpadding: 40rpx 0;\n\t\t\t\t\tborder-bottom: 2rpx solid #F2F3F6;\n\n\t\t\t\t\t.time {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #ADADAD;\n\t\t\t\t\t}\n\n\t\t\t\t\t.box {\n\t\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\t\tdisplay: flex;\n\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 100rpx;\n\t\t\t\t\t\t\theight: 100rpx;\n\t\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.mid {\n\t\t\t\t\t\t\tmargin-left: 20rpx;\n\n\t\t\t\t\t\t\t.top {\n\t\t\t\t\t\t\t\tpadding-top: 4rpx;\n\t\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t\t\t\t.name {\n\t\t\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t.level,\n\t\t\t\t\t\t\t\t.promise {\n\t\t\t\t\t\t\t\t\tmargin-left: 8rpx;\n\t\t\t\t\t\t\t\t\twidth: fit-content;\n\t\t\t\t\t\t\t\t\theight: 28rpx;\n\t\t\t\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\t\t\t\tborder-radius: 14rpx 14rpx 14rpx 14rpx;\n\t\t\t\t\t\t\t\t\tpadding: 0 14rpx;\n\t\t\t\t\t\t\t\t\tline-height: 28rpx;\n\t\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\t\tfont-size: 16rpx;\n\t\t\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.bottom {\n\t\t\t\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\t\tcolor: #ADADAD;\n\n\t\t\t\t\t\t\t\tspan {\n\t\t\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.price {\n\t\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t\t\tcolor: #E72427;\n\t\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.down {\n\t\t\t\t\t\tmargin-top: 32rpx;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: row-reverse;\n\n\t\t\t\t\t\t.btn {\n\t\t\t\t\t\t\twidth: 240rpx;\n\t\t\t\t\t\t\theight: 80rpx;\n\t\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t\t\tline-height: 80rpx;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.footer {\n\t\t\twidth: 750rpx;\n\t\t\theight: 192rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\tbackground-color: #FFFFFF;\n\n\t\t\t.btn {\n\t\t\t\twidth: 686rpx;\n\t\t\t\theight: 88rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 44rpx 44rpx 44rpx 44rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tline-height: 88rpx;\n\t\t\t text-align: center;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_master.vue?vue&type=style&index=0&id=105ecd58&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose_master.vue?vue&type=style&index=0&id=105ecd58&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756107649786\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}