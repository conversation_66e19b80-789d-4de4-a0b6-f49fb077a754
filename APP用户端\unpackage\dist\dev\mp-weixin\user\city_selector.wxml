<view class="page data-v-b81bd106"><block wx:if="{{flag}}"><u-picker vue-id="4b5d4dae-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeHandler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-b81bd106 vue-ref" bind:__l="__l"></u-picker></block><view class="top data-v-b81bd106">请选择你所在的城市以获取周边服务</view><view class="main data-v-b81bd106"><view data-event-opts="{{[['tap',[['goMap',['$event']]]]]}}" class="main_item data-v-b81bd106" bindtap="__e"><view class="name data-v-b81bd106">服务地址</view><view class="address data-v-b81bd106"><label class="_span data-v-b81bd106">{{form.address}}</label></view><image src="../static/images/position.png" mode class="data-v-b81bd106"></image></view><view class="main_item data-v-b81bd106"><view class="name data-v-b81bd106">所在区域</view><input type="text" placeholder="请选择所在区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e1',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-b81bd106"/></view></view><view data-event-opts="{{[['tap',[['confirmSelection',['$event']]]]]}}" class="btn data-v-b81bd106" bindtap="__e">确定</view></view>