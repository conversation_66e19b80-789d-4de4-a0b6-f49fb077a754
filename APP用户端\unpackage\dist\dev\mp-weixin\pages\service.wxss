@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* Added: Styles for confirm popup */
.confirm-popup-overlay.data-v-fe340868 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1002;
  transition: opacity 0.3s ease-in-out;
}
.confirm-popup-container.data-v-fe340868 {
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-sizing: border-box;
}
.confirm-popup-content.data-v-fe340868 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.confirm-title.data-v-fe340868 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.confirm-text.data-v-fe340868 {
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
  text-align: left;
  width: 100%;
  margin-bottom: 40rpx;
}
.confirm-buttons.data-v-fe340868 {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.confirm-btn.data-v-fe340868 {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.confirm-btn.cancel.data-v-fe340868 {
  background: #f2f3f4;
  color: #666;
}
.confirm-btn.confirm.data-v-fe340868 {
  background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);
  color: #fff;
}
/* Existing styles */
.large-promo-overlay.data-v-fe340868,
.activity-popup.data-v-fe340868 {
  transition: opacity 0.3s ease-in-out;
}
.large-promo-overlay[hidden].data-v-fe340868,
.activity-popup[hidden].data-v-fe340868 {
  opacity: 0;
  pointer-events: none;
}
.content.data-v-fe340868 {
  transition: opacity 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}
.content[hidden].data-v-fe340868 {
  opacity: 0;
  -webkit-transform: translateY(20rpx);
          transform: translateY(20rpx);
}
@-webkit-keyframes hand-press-data-v-fe340868 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(0.9);
            transform: scale(0.9);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes hand-press-data-v-fe340868 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(0.9);
            transform: scale(0.9);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
.pulse-text.data-v-fe340868 {
  -webkit-animation: pulse-animation-data-v-fe340868 0.5s infinite ease-in-out;
          animation: pulse-animation-data-v-fe340868 0.5s infinite ease-in-out;
}
.large-promo-overlay.data-v-fe340868 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}
.large-promo-container.data-v-fe340868 {
  position: relative;
  width: 600rpx;
  height: 480rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.promo-background-area.data-v-fe340868 {
  position: absolute;
  top: 0;
  width: 100%;
  height: 252rpx;
  background: radial-gradient(circle at 50% 120%, #ff8964, #dd4a34);
  border-radius: 14rpx;
}
.promo-ac.data-v-fe340868 {
  position: absolute;
  top: -100rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 450rpx;
  z-index: 1;
}
.promo-character.data-v-fe340868 {
  position: absolute;
  bottom: -6rpx;
  right: 18rpx;
  width: 132rpx;
  z-index: 3;
}
.promo-coin.data-v-fe340868 {
  position: absolute;
  bottom: 72rpx;
  left: 24rpx;
  width: 54rpx;
  z-index: 3;
}
.promo-foreground-area.data-v-fe340868 {
  position: absolute;
  top: 200rpx;
  width: 348rpx;
  height: 180rpx;
  border-radius: 24rpx;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 24rpx;
  box-sizing: border-box;
}
.promo-price.data-v-fe340868 {
  color: #f82c28;
  font-weight: 900;
  display: flex;
  align-items: baseline;
  line-height: 1;
  text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
}
.price-val.data-v-fe340868 {
  font-size: 96rpx;
}
.price-unit.data-v-fe340868 {
  font-size: 30rpx;
  margin-left: 6rpx;
}
.promo-subtitle.data-v-fe340868 {
  margin-top: 12rpx;
  background-color: #fadda6;
  color: #f82c28;
  font-size: 22rpx;
  font-weight: bold;
  padding: 5rpx 18rpx;
  border-radius: 18rpx;
}
.promo-button-area.data-v-fe340868 {
  position: absolute;
  bottom: -10rpx;
  z-index: 5;
  width: 450rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
  cursor: pointer;
  -webkit-animation: pulse-animation-data-v-fe340868 0.5s infinite ease-in-out;
          animation: pulse-animation-data-v-fe340868 0.5s infinite ease-in-out;
}
.button-image.data-v-fe340868 {
  width: 320rpx !important;
  height: 100%;
}
.hand-pointer-animation.data-v-fe340868 {
  position: absolute;
  right: 30rpx;
  bottom: -12rpx;
  -webkit-animation: hand-press-data-v-fe340868 1.5s infinite;
          animation: hand-press-data-v-fe340868 1.5s infinite;
}
.hand-pointer-img.data-v-fe340868 {
  width: 48rpx;
  height: 48rpx;
}
.promo-close-btn.data-v-fe340868 {
  position: absolute;
  bottom: -80rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 42rpx;
  height: 42rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 4;
}
@-webkit-keyframes pulse-animation-data-v-fe340868 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes pulse-animation-data-v-fe340868 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
.activity-popup.data-v-fe340868 {
  position: fixed;
  right: 16rpx;
  bottom: 600rpx;
  z-index: 999;
  width: 120rpx;
  background: linear-gradient(180deg, #FFE1C1 0%, #FF7B5A 100%);
  border-radius: 12rpx;
  border: 2rpx solid #FFF;
  box-shadow: 0rpx 4rpx 8rpx rgba(0, 0, 0, 0.2);
  padding: 6rpx;
  cursor: pointer;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}
/* Location banner styles */
.location-banner.data-v-fe340868 {
  position: relative;
  margin: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  padding: 24rpx 30rpx;
  z-index: 999;
}
.banner-content.data-v-fe340868 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.banner-text.data-v-fe340868 {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
  flex: 1;
  line-height: 1.4;
}
.banner-actions.data-v-fe340868 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.banner-btn.data-v-fe340868 {
  background: #FF6B6B;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}
.banner-close.data-v-fe340868 {
  padding: 8rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.activity-popup-shrunk .popup-content.data-v-fe340868 {
  opacity: 1;
}
.popup-content.data-v-fe340868 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: opacity 0.3s ease-in-out;
}
.popup-header-image.data-v-fe340868 {
  width: 70rpx;
  height: 40rpx;
  margin-top: 4rpx;
}
.popup-text-main.data-v-fe340868 {
  font-size: 20rpx;
  color: #A34A00;
  font-weight: bold;
  margin-top: 6rpx;
}
.popup-text-price.data-v-fe340868 {
  font-size: 18rpx;
  color: #E53935;
  margin-top: 2rpx;
  font-weight: 500;
  white-space: nowrap;
}
.price-number.data-v-fe340868 {
  font-size: 24rpx;
  font-weight: bold;
}
.popup-action-btn.data-v-fe340868 {
  width: 90rpx;
  margin-top: 8rpx;
  margin-bottom: 4rpx;
  padding: 6rpx 0;
  background: linear-gradient(270deg, #FF5A36 0%, #F60100 100%);
  border-radius: 18rpx;
  color: #FFFFFF;
  font-size: 18rpx;
  font-weight: bold;
  text-align: center;
  -webkit-animation: pulse-animation-data-v-fe340868 0.5s infinite ease-in-out;
          animation: pulse-animation-data-v-fe340868 0.5s infinite ease-in-out;
}
.close-btn.data-v-fe340868 {
  position: absolute;
  top: -14rpx;
  right: -14rpx;
  width: 28rpx;
  height: 28rpx;
  background-color: #888888;
  color: #fff;
  border-radius: 50%;
  border: 2rpx solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  line-height: 28rpx;
  z-index: 1000;
}
.page.data-v-fe340868 {
  background-color: #599EFF;
  min-height: 100vh;
  padding-bottom: 80rpx;
}
.content.data-v-fe340868 {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  padding: 0 30rpx;
  padding-top: 18rpx;
  padding-bottom: 50rpx;
  overflow: auto;
}
.search_position.data-v-fe340868 {
  width: 630rpx;
  height: 72rpx;
  background: #F1F1F1;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 15rpx;
  position: relative;
}
.position.data-v-fe340868 {
  font-size: 28rpx;
  color: #333333;
  margin: 0 12rpx;
}
.shu.data-v-fe340868 {
  margin: 0 15rpx;
  color: rgba(0, 0, 0, 0.2);
}
input.data-v-fe340868 {
  margin-left: 22rpx;
  font-size: 28rpx;
  color: #ADADAD;
  width: 260rpx;
}
.btn.data-v-fe340868 {
  width: 112rpx;
  height: 56rpx;
  background: #2E80FE;
  border-radius: 28rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 56rpx;
  text-align: center;
  position: absolute;
  right: 20rpx;
}
.img.data-v-fe340868 {
  margin-top: 20rpx;
}
.tag.data-v-fe340868 {
  display: flex;
  justify-content: space-around;
  color: #BDD4FD;
  margin-top: 10rpx;
}
.tag_item.data-v-fe340868 {
  display: flex;
  align-items: center;
}
.tag_item text.data-v-fe340868 {
  font-size: 24rpx;
  color: #333333;
  margin-left: 14rpx;
}
.grid.data-v-fe340868 {
  margin-top: 40rpx;
}
.grid-container.data-v-fe340868 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.grid-item.data-v-fe340868 {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
}
.swiper.data-v-fe340868 {
  width: 690rpx;
  height: 90rpx;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  border-radius: 16rpx;
  border: 2rpx solid #F7F6F6;
}
.swiper ._span.data-v-fe340868 {
  font-size: 32rpx;
  font-weight: 600;
  color: #000;
}
.swiper ._span text.data-v-fe340868 {
  color: #E72427;
}
.swiper .shu.data-v-fe340868 {
  font-weight: 200;
  color: #E6E6E6;
  margin-left: 20rpx;
}
.welfare.data-v-fe340868 {
  margin-top: 38rpx;
  width: 690rpx;
  height: 325rpx;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAVkAAACjCAYAAAAgjG0QAAAAAXNSR0IArs4c6QAAIABJREFUeF7tXT2sJdlR7r73vXnz82bW612MbSGDEAg5JiIACYmAiJ8UEVrrDAKILDKESJAISCzjEBGbiIQAQUBAhsFrm11je7Gw19r1/sysd97Me4363r59u/v81PdV1el+987dyJ5b9VXVd+p8p/q8+1NXkf+a5vHPVs+qP6jq5veaZ88/f3P100fV9fVFc3M9tm6i3rF/JP6tA22qKgovIQ2cVg8uq9XZueRxhK/XR1jTgZd0WpIDX8BJ+nX9tK5XH1RV9XpV1V+rzqu/r+vLH8WKHC190zTr6tmTP2qamz+7/vC9T16//07VfPxRVTUTuZv+/x1y4t833qRP0p7Aqu/cqVb372OLG90EJXfGBNs1FAkmmLcvt2u4NSOxEfZ7yALYffwEtjmkESDrbsTOct9hO4bYQ4GgoNm4DJVTnIkAisSuV1V1fqdaXdyr6ou779bV6s+r8wd/U9f1aBrtUZumuaiun3z1+qMnf/j8R29VzdXTbWIO4pgVWacYPYvDfOu6Wr/0SBaGJLck6YigxITKPQwJCIssiYvyUVxkM3mbSzIAiK6iAcrwdAzb/n8n+PEhTADT8WmHPD9WkR2ir8+q1cNPtE/Of1edP/xCXdedgHY0N01TV9ePv3L94QdfePZ/3xsLa0pkSXFUTbNkjJjQrh5eVvX6jCSbaBRVm09W17V3SDDAnJ5QGE6KC6ywlkD9iubBGMjGNieWyaHDdgoxFlkQFDS7tVNslN26Wj16uVrdufvV6vzytbquN7K3KbW5+uC1m6unX7566426urkJ3dlpNiGOswntIN/63r1qdXGh2CeqLsA213SEcAtFAsET7K4sEl9iYxaBzYisuRwjQNLdiCvxvus/pzB7kQUBQbOwDLWjoNVOuN3jwfrlV5t6ffZafefRVzfd1zQ/flg9v/ftp9//9qebj3+aXp4lhdYwTYv3srM3+uFMsaoJRdzgA4NZRDazgcx7ywBwJFPs+AgD+QDNDmuKHWS7PqvWn/jUD+s7l79c1/Xjurl68sXrJ+99eXNNkJhAs/+u8FFNtFqRX6+r9cOH8a1/VAIrPBbHGIAnWdWukOW2uMgCBcpZZiyUvIhuooEy6w7XGX4PBwKDZvsiaQfyydUZv6qq1cNPtk/QX6zvPPpK3Tz78B+ufvjW79x8+N4+MVbQFJNmo/BR/RFu88evl14AkSUbBTCnNw+z9YsLrHDoAPWTOxWv/oim2D3LBKGE6ZZU2iG9FgGUI/bwIe3iXrV+9MrX6vMHv183z5+8+fQ73/jF5vmzcWIFhXb3dqCk0HoKcFW9ACJLNgpoXkxkZxHYzOYE6yd2Ki6womaYk5Mnb6cQLcx2L5OApLmbyM4ksJsFWK2rs1c/82Z99uCX6ubZ4ycfv/H1+9Ep0UtoF37/7PrRo6qqJwwnF5ruAHCDofFBuJEZmTNovjUDjZm0ZxHZTN7mkgwAoqtowDA9nK0KDoVEzoRpwYQ7XuhkcO7rujp79bNP6vPLy1Zkm4//+z/izp4TZQRrrvfPrh49quqhyM4usBOxcl9bElAwV08oaAsWF1mgQDTXqB3J9w5DdBMNDFnX7uflPlswb9BsXyTtkOdntkl2G+jsZz5b1eeXdV5kW0t2miV9VH8EI2MEd7JHJbKKRoQ1SIEtyUBxgRWmb3NJBgDRVTSQ2M1fFTjDb+EIUML0oKfYjpOxyL7x9bSYkoK2IYecgIsKbfuHr/a6YPjf7CI7CUg3W25vKcCWEtlZBDaz8RVUgY2DiZ8YXzTA4gRWHa4TfAtzuouV92QoshoxdfRRCS0yZa9W47dwzS6wk03v1OjqRyogPj2hoFt/FpHNFAjUTj5vopUDg5k5uUQuvgI77mYiZ8IUIMvIO50MEW+PHRdZjWiSU2tu0i3xtq767KxaPXggrFtJ0gdt6R6GBAQmWNWEgrZgcZEFCkRzjdqRfE8xljrgjWmnywCBQTP14CCtaRCfTkiK0L0+xh2J7NM3vj7+WkFkQhyGZe0zYu4ttPXFRbW6e3dBkR0Q7762JCCsQSQu0oLFBXbyxAALHJK8gI1AZCktwHefU6k/eBE5E6buU2ywdHQyyOpiIrvRvR2c43TK3M+WeP/s6t69qj7vvlN29kliEtB9fUnApUR2FoHNCCFJU7irjACiu2hAbPShaYfrBN/CqL7yko5PO+T5Waj/gkm219c5hXaG989uvoWr/d7H5B50XtDRch/OFLunpwAfszR5Jm9zSUaAI5liVSJLU0c7gALr8DSSjRTmHRXZfpKUhLbwpOv2/tn2j16Xl3tqAh6cFzRYBN9JIlMI0WjxOU01oaDzVXGRhUd0NOOJnbFPku5GXKma6QdwJHvh9b3IgnmDZuOwKqd45rPt93jO8iS7uT/I/ACM4z1sLI7q3QaTnDffwJW9j3Vc0JTAFjlAybwBDSovsmTOlCCcptjo0elM+R4OBAbN5hFZVTJgF0aw66r9xNf+wwhP3/zPkaCOpNVTaEksq9C27yqo1+vM+6VLET/BdQ9DAsIiS+KCLbh9b3Yh7PQ9kENIY86iu2iAMhyfvJ3g9xPsLgwIDJqpn9Akdvr4dCIScv5JpwsXiuxkCoSElhTNTWakj0po2xirulpfdl9xGOW4JPEDbPcwJCBgTk8oTAsWb/RMgUDtpnsWiYdsfHNymei+7ygYiyyYN2hWXmAXOOCzIusptKSY5gRY87auzVXB7hcRggWnO0DaTkUnCXUjwhMsOaGgbCwpsC57y9gnSXcjrsh/KZEF8wbN5rkmcGmEBOOJQhmR3WjuEJ69h2XtM5MuK7SrywfbdxWcptjkllRNKOIGHxgsKbKqjT4szgiw5BTrrCv0k46KOpVTvBtHUI64QbQI9uCf4tcFO5CJOB7a+2fb98Vu/uA1+yQxCei+viQgPMmSuKjQLiWy5nKMAKK7aIAyXPQJanwIE8pNl0c7ELc8ztijyBaR1VwbZKZQ+pu8jO+fvRVTLNGT2I4imwUwpycULNGt1VIC68I7QF6OiyOZYsciC3ICmh37VUFbX36SjYjsjvANOY7XAAwW8v7ZfoqNbjZVBxDSUmqSJfOGJ9hdaSS+xEhxgRWU1FyOESDpbsSVeD/9Cm18shd50xjkp1hMZOeaZknRloR29eD+8nexLtPUcOHJzUmJLImN9OOSImsuxwhwJFPsuIVBTkCzw59iExt8Uv94kv3Of0HTKfRHMFI0s5NxAisltJt3FNy5c7qLhUVWtStkmS0uspm8zSUZAERX0UDmNmrR4TrB755a93AgMGi2L4F2yPMTwDnj99ETuKLIguIICa3j27dS1wmB0LYfob1/b3sZGOWgFOGRU801FAkGmNObh9n6xQX2Fl8VHMkUOxZZoKHUT24gNtJ/t0xgw+uCdpLNiazm6mCGe9vh27pW9+9V9Wq9reNoRJZsQsFcPaEgTT7incwbxScnCRrW+qm0ZNkz8eEUZt8nICBoFq6H2lG4cXDCzT01DF9LhAuvC3ZOoDiaJlowxqjG1DsOmqbqrwmyJ2op4ie4rmFIMEpkSWxEsU5TbIKlAlxPDxzHEPSTjiq2yinO72xTbEJgaJEFrw02ZpIwE1g9e8RVw+ZLTdaranW3vSbIfTzecUFHy1xSYLMFgc0WHvinXz7InRbKPhHdRAPkCIvYdLjO8OVFtljCHUfO+MonqPQkmxPGyWvQNOsptFMBruuqbr+Ue/eVbkluZyLdNQwJBpjTm4fZ+qcp9iim2P3RDjTUrmLCVJiGmI5LQNHJEDEj2JlweZEFhXHp75/dCOyq+0Luo5pkyUYBzYuJ7CwCm1lgsP70bjICZN2N2FkJ8J1kWzTVkw5dIu0APrk54SY5n+AL4WSRJYRW86hv9anvXlT1+mxPx2mKFU/kLUUFGnEWkc3kbS7JACC6igbiumUVxhFedQjT8WkH4mx0xA6iclNs646J7NJCm7ifbf/Q1f4Kbf/f7AI7ESv3tSUBBXP1hIJu/+IiCxSI5hq1I/mGH5OVuFAtvt+0Ne5oMG/QDNioUMWy7tEJgXETuNQk+z/foL/ndZPdQASh+1nij1qpSbf92Gz/w4his5civaTIKnKGNUiBLbVhcYEVpm9zSQYA0VU0kNjNvF5KZImcCdNtIbTDwU6x4STbiiw4tQZVewktIMDBBLuYyE6axbF3VI24lMjOIrCZzWnm3QgguosGSpHtcJ3g1U86dHzaIc5PAOOEm1wN3X4fXxdIIksIsGmiTQptU9XnkyuCxQS25BSrOO2B/tqaAIbslp9FZDN5m0syAmTdjdjZtfCdYveZEjkTpi/iFJueZHNiqhFaYDrNTca9jl5cjN9FsKjADgSLbjRJxUhAYIJV/bVYSjPgn8wbxZcOBnNYI0DS3Ygr8VPgV2gpIaTLox3yDMw2ySbyBsuJT7K70oyfyIKmWUS02/fBtn/kGr5Na0j/Ik0+CAqSLe2Z/eskICCy1ObBE53h+2KF6ZukKizNCHAkU+yeZYIPwrRI/43i08kQXR7BJsLlRRYRwGmqk7vZNhfxE2E5MV+tq9Wd86rKndqzi+wkIEE4trIk4FIi28cl88VIGFjZJol0OGPeortoQDMxEisn+N0e3cIRoIRpWZGlEyF5t+13WWQdhLaviLw2aN+eNXqLVoqaKMcliR9gu4chAQFzevOgLTiLyGYKBGonnzfRygHNMCeXycX/Lnb3gSJYZOnyaAdi6ZyxR5Ej2GQ4m8iCAkxfG7TXA+1btFLXA1P6g6JJFrittT/t3cOQgIK5ekJB+SguskCBaK5RO5Jvse92BkZcqaYCd7HUnb2qPJVTnInZ9nsiZ7KUsch+93XoS7uDyoG7W0ho22/SQqfXbD+TLEhNnTrZ3MOQgIAG0RMKw8UmPpkzg5/DNoc1AmTdjdhZjnyn2PEKgnmDZuMyVE6AyDriBtHsU2wLGYosOJ2O8gGvAbJCu5temVM6yW8p4ie47mFIQFhkSVxUCJcSWXM5RgDRXTRAGZ7YdbhO8C3M/hAmDkw6Pu0AXhU440oiqwznI7KEMAdC24prO72iVwNDIqJFK5mA2n6A7R6GBATM9yaAMVT/wKiHLIC9CZPBNYc0AmTdjdjSFEtoobSkY5EF8wbNTlPsnoG4yBKiCU20k0l3c3q2E+t6rRPXXdBgwVUdIPViqCzuYUhAwVw9oaBMLCmwLiJD8j3lJeluxBX57/Cdwuz7BAQEzcIy1I6CVjvhShNsUm/EBdsYpEVWI7TItcFqtfmC7aoefDUhlitwOJYifbKz3cOQgJTIktjIWiwpsuZyjABHMsWOOxrkBDQDNirSZYJWq5IB40awDeHyIusptOtV9xPdhmzFE8UBO7oME1z3MCQgLLIkLtiC+yf5QvipqwJzOCOA6C4aoAxP7DpcJ/gW5rDvYl0eZzJrcVtEVhLg9q511f4ltJ1aRXXkmi/abE4dOLvIknkD5nsTwJhj3n8t0Uc1l31l5ONIptixyIKcgGbzTLEuzZDo/EShqvq3IUaT7NX3Xo//8rb0Fq1NAq2g1tu71mRChkyHlAQwTrhJwZngu4UjgQRz9YSCCq33YYkcaNl1RxN3OuRL97XUf2S7pOD2fQICgmZhPLWjoNVOuGj/GcMFIhsq5PjBYpQXFZwyTu+eKIwTNkK6aygSjBJZEhvRqyVF1lyOESDrbsTOct9hO4bYQ4GgoNnhT7GJCVlV/56Nich+M7/VTMFMztu8Zp8kJgEdSjA1Iiyy7olO+C+Ef1vftiWWKxogR1jExldgW7TDv4vNCoGSZ+FJx7i8RyCyRgaQKcLpabOkwI5brwAnS06xLvvKwMmRTLFjkQX5AM1MvZ3bg0F8VUKA+CZwHcLNJLIOmSY3mhP2rFcFZM7wBFvkNJjhj11lJwnzR39nf4Ka8EG2S0pR9iILAoJmYTy1o6DVTrjIXnc52LeBcJE11WdyPsKrApIPSmRJbOCMd393CNrkbmeGgZMjmWJVTzoq2lRO8S6cbYpNKKpLKXV19upnqvr8sq6bZ4+bq+9l7mRNAU3Op7tYWGSNPOdGoM1rhfAlXHNYJYDoJhogR1jEpsN1ht/DgcCg2b4A2iHPz2wim8jbpZxZRNYh0ySEAzYyVbmGIcEAc3rzMFu/BwcSYXBHthlsc1gDQNbVgCvyVFJkibwJU+FxU6w4MJhNYEtOsVtsbJKlyXZ7zjuyqwIFkYBLMZGdRWAzEzJQOzkKcZv9SES2LYP6vlj19jUvWGK/O+EmV3+C7xZuC3T7RfZFnmLBJ/QtRW6dEXn6K4DdRznEKbYQ38N1dKRcdQjT8WmH9IF3RFPs7RfZ2QV2snkc+0b1OCXEV08o6DxXfJIFCkRzjdopF1B0Ew0MWR/il3I78zGbyCbydisHnWTVAdWOkUlq2rMO2Mijg2sYBRisQQpsSQaKC6wwDZpLMgCIrqKBxG7m9VIiS+RMmKqGhxw7swlsov/o2lPF7IHy1wXqgGrHccZJGCf8gJ8JrmsYBdhSIjuLwGZEVkEV2DiY+InxRQMsTqr/nOBbmNNdLKHoTrxPr+5ur8jOLrCHdVWwz9atMyJPEAWw+ygZbHNYI0DW3YidlV/fKXafKZEzYXqaYuUptrW4nSK7iMAORJZuNGlwIQGBCVY1oUhp7l4vPskCBaK5Ru1IvuHbKCOuVBPz+3YS1uhPoWDeoFnkNAayAUyC+HRCQJCST1DBBtr8Q1pk1fWpHYG1c8BOLsMA2z0MCQhrEImLtGBxgc00ufASkr75XRZHMsWqnnTodqId8ks4gnPGHkWOYLuFC4HiIqsOqHYckz/7JDsJ6FQGcGrEm24pkZ1FYEtOEsaFE91FA+wcCKw6XCf4Fkb1U/B0fNoBFFln3BTf8cFTuYbpvj4gkS1J/OFMsaoJBW2bWUQ2s47mJTYCHMkUqxJZmjraARRYl8eZTKx5p9j0dYGaP7WjMMk64UpXBe5hSEBgglVNKLdGZIEC0VyjdiTfU4ykuxFXqqnAXSx1Z68qT+UEPrk5YkvXBK6aHs87nGTV9akdgasCJ2xpY7qGIcFo/SHxxY3u/uwUiXiaYkNSfN5RMJ5eSeVQtZLKKVo+IABS94Kvzz/FhpPs94VfRpAmQbBUHsZpQYPAE1z3MCBguEM2mQ7/eTfBUhMKuh59IDBfFFfi203XjXmL7qKBkpEOVwmf7g+SWDo+7ZCeYEc/1eCEiwqMW7g80HiSXVJkk3m6MZGfrNzDkIAJc9OEwmz9Pj6ZNxzjlk6xYrmiAcxAqSk2PreCeYNm+9xphzw/Izhn7FHkCLZbuEMQ2VBJOnrcWEgstG2SSHcPmffE3G1CQbf/kgIbVwg0c1ufJPuOnATJbHvz9i428zulEmy6T8D+A83GAmtIeFpQEJ9OSKIo3x9u4YqLrFOmURgn7OhSDLDdw5CA1JBHYkttWFxgBRU1l2MEyLobsbPc+xzw4TlB5EyYuk+yswlsov9UtccWVAYyXhfIAaQ9vnk9CeOEHyQxwXUPQwJG0hnfwSo/gy6R3+/QcKtKrtzrCT5ImsKYRgDRXTTgaNiPsNv/pYQfrta0TyhgOj7tEOcnaDcn3ORqlNrvWN5GkTV0ivhEhhWg6/IBtnsYEjCjP+GDGYmNkNNDFsCWlMQc0giQdTdiS1OsE3wIAwKDZu4TbHLf0wkh3Z0+yVzCYSBGkcWCZA+Y6BWPEVekv8MPTlTRUTAg844csPEJVjyRdIkvKbAO57N6FBTpJNeRZf/g7mJFwjgGAnpL8Z2ZYLiME9ZY3gaRxQKItURhnLCjwSPKJiaJGpB5Z8zVEwqdKpkzit/blWp0Y95LTrEOB0x8PgA5Ac3GS61yCrtlNoFNHAxOZTAHvFJknTJNwjjhB0tcUmAVOweeZJ35CHaoM/5BC6xiHeGDp+PZSPdw+cYPgiAwaHZ0VwXxkwlePS0fSpF1asQlp1i60aS1IAEzA97R38W6tA/J93D5lpxiDWnnSwCBQbMiU2x03VUJSZvxVtzF7pJUiqwTMUs9OridaMq7KkBgVRMK0Hobkz6+0zom42YKRXON2hnznv0JqividBc7WU3jOmb7LvLHHrdwHJBCZLkA7P5j7jq4fTrJ26mMbQ4kWMY8fInElkgZwTljj2IfmsAq1lHiOsaHkfL4fACCgmbzTLGqZAjGS+13Pm+FyDo04+yTRCnCfSbZ4cYp+h0FQbp8w2BdDozqGFBk8jF84kgsVzRQZd0fwkb4WJ9Qhzwdn3bI8xPAOeP30SP73dA2+6J0+SpEVhdIHnIccJGx2T0MCUjpD4mNSEAPWQA71eTDvMxhDQBZVwOuyLvPN23txhvVVZKqPJVTnI2lBFY5B6WXlOeEFFk+QDTZKOEuR02Cmy7gcBQQNwZiQPIBCKxqAyGpjh5AyLxRfElkzWGNAEl3I67Ez224i1VtLydeZhPYhKI6lUFfC3bpzC+yszf6JKAb4cprE0BoqUdAaYNHp0hXEiKP9JGkzCGNAKK7aMAwPbAdHPBKhPgES/YfXR7tQAx/jthB1FL7XZlzXVVnr3ymqs8v67p59ri5Er/qUBlIHNmNuMnlLUW4WBD0yDQcrE93sYgCKftEdBMNkOTSJ4sRPtYn1EFMx6cd8vzMNslG9rtqgk+VQ/LSLRwxyZIBYnlGIRxws0vsM0mEIci8BfPxyyQ2IgE9ZAFs6ZqAHLqgEwqpGToLS/LRFe4QYii0ZQXWZbH2qzObwCbyduCe4nval/gk65RpEsYJ/7Y+OoQ7ZJOpeUJBhWYWgc1sTvPyGgFEd9EAZXpiZzvgY/1B/TJGou/kYpz4mFVgI/3nVIb2Lnb3zk7wusAh26MRWGg8ggdf9YQi75TINOGwjsm4CWyXkAYQ0VU0YJge2NoENjIQhdhIZnR5tEM6i1lFNpK3SylKkIHbPCI7u8BOhFDJE9E9+XafxDdPKMjmGtoUn2QzAutyJ6ZcQHGSU+KK/HeBjb9CG+sT6tGVLo92oPpePRFCfEeM3MohgSZ9B4gsGSBGyCIiOwjqUEJkLBSXvr8TyFiOU3NNdLIfC2D3dWWwzWGNAFl3I3a2A3zeGxueE2DOoNm4BJVTnIUAyhE7iBjBdgunBBq4CSKrDJB/1tE99mCS1llN8nYqY3yLCiQU7pBed03faA+EHgu8GwGJyAl8c1gjgOguGqBMT+w6XCX8sG1U7zpJ9J1cjDLh2H5/AX6FNsnnhMbyIptcN6cFjVY6wHYPQwJm9KfoBw9aXvrYZM7ybsQOSnNYA4DoKhpQLARPOk7wIQwIDJqpn9Akdkbx6WQk9Hz/uYVTAEVcyops8kRVJE/Q7vVZ8TAkmffE3DyhUBzMIbICHyRdZr53AOIkZ04svxIF7mLpdxVQveLMRwDnjN/XlplgqPpTxmTeib7LiCwZgMrTCXvWKZbMmdYfEl9qoh7OGXcUN4NtDmsEyLobsbPcL3wXO3qCkZpk+LoTJ7MJbKJQpzLUf6TDJ1mnTJMwTvhBD0VGR6bPRFsw78SJNvxn1V2bmF9n0AcC80VxJb6Hk6QaU60S+4hi2aKBMvsOVwmf7g+SWDo+7RDnJ+h7J9zkapTa78q8E26JSVYZBDoYHbAR0t3DkIAJ81B/SVx0+/ewhfBz36FrDmkAEF1FA5ThiJ3PFBs/ZsC8QTPiROL4GMWnkyFiRbDdwimBZhXZUEkGIxbBI21qmyTS4UjSIwfs6VdomcUk+R4Oetn35Spx0dRvw7dtoblu7JIblULpjQN6S/GdmWB0mU+8lHnjIqsMMC0uCuOEHSVygO0ehgTMmIcvkdhSE/VwzrijuFSBUsY+DQ49Uc/AiTFEKHsEIGHqPsnOJrDxOZ/9cRK3gQrou8l1wbfIDZEwTy62qguAnCKjI+CFm5B5w5MsiSsl3O9Q5wkliFtqkjDyIbqLBhLD+YZXwg9Xy/T+aTo+7RCvP2g3J9zkapTa78q8BbfInawykKjoRtxs+w+w3cOQgBn9OfpfoSWpCpfUCJB1N2JL/ecEH8KAwKCZ+wSb3Pd0QuABV+qAT0zISFacyBon2eQAVYrwyQon4yNMxWzIvCMH7OkuluGe5HsKnXQ34kolHNxdrDgRSRWPXw/oLcV3SYE9FJFN5lmK9EhA11AkWMZcPaGg7d4HIHNG8Xu7Uo1uzHvJKdawP4dyp37SUVGncgIePpxwj+SqoC3D90529kkiMjrSopFzIBsGnmRJXKmmYIJ3xj9ogXVQQGnDG+keLp/qo9Z0fNoh34FLTbJB30sbJfW6kg/QbQaRBTNR8TPAdg9DAmYGPPWEwnBSfJIV+CDpAkYivPolp1hz3dsyQxgQGDQTnu9xrqeWSwns8BFAn33nqSIR/FBYXZ298unhb3w53MmOClYmD5PW4budaMqVAwRWNaGgPBQXWIEX8zIbAZLuRlyJ/9Nd7IShUnwnNrhbOCWQ6LY18BPZ2Rt9ElAsWNoxw9dJsIy5ekJB0x0FIPNGYyRmrdw/49DGnJecYuMjKF568uMAICeg2TxTrCoZgqtS+12ZN+TmKbJHJbCKnRNZ//i7CqCVIRpv+rRTCD/1Tu/EgIEXYAQQyxUN8FRjT2hG+GH1qicdOj7tkOcngHPG76NnNphyBbduynxht+IiC2eipKnDdw9DAibM4/JBYiPM9JAFsFNNbhj6w5IMeWddDbgi777fUTCPwBpEJcbHUgK7y8VteRVAkMveyOe6IEq4y487Jdp9ILCuYSD29jkBAqvaQOImn2uCFTqapMtVYLOaYU5MmODqqjL0XWyCpb8vVhXfiZfZBDbRf05llJtkxwnaRTZZsBsTk4af4LqGUYABQmt6NMlt9yWnWAVV41KMAKK7aIAeZfH+M8IPhVY8taFJUirHmHD26cUROyij1H5X5gy7eU2yRyWwisepyPqf7mKlze5w1yA2umjAJDmw7XCN8LFJljqI6fi0gzDJT192xu/hMxtMuYKmgz5+MkYy8Zxko9wycEkGAAANj0lEQVSWInzy6OAehgQUzMcvk9hIA/WQBbBTTe6gj6qpjdrTJfnoDmKHEOF+BUFBM5OYQE9Pk/2I9CxtEylWVb/LowB4DoYJ2q4LjmaSJVcucaKZJxS0CWcR2MxkT9IVlmUEEN1FA5TpiV2Hq4SP9cfpLpZQdCXvbv0nxo8Lg15kj0ZglSdyon71hMJs+1lENlMgk2vUVuzWdATRVTRQZm8T2PwwTuRMmIKjF85HEJtOBo8Ve3uVSzglCOzmNcnOLrC3+6rAPKEQrbcxLS6y8AnCZt7Zwx0bPvUu8ssH3QoX+BVaSghp2miH/HrOJrIlD/jME1qq+viAGrGO562bZBcR2UFQ194hwQTz8cskNiJZxQVWaEJzSUaArLsRO8u/z3tjw/0K5gyahacS0lSAzWwCm+g/Vf2xupRAolva4EBEdlKAWDDQNP1ISLzhMHGiDf/59L7YHPfGhRPdRQO0MSZ2Ha4SPtYfB3cXO2psJREw+yX3O5zE3hAuN27Ii2wyIJyJrUr3MCRgwlw9oTBs9LHJnJkYp1+hdRXYKfXhyoFrCZoplAHrjlF8OhksRj/4xJeAAEmYKvKGXbwm2cQkp/7kBMyabZJIh4EZ3EJEDtgX4n2xOwJJukLelQDJvnNLLN+JBe5i6UkW3iuJx23KXxI55TqKOWQmGNEXMSDzFvsO6z+nSZZMHuGjtxlgu4YhwQRz9YSCcvEiT7GibpBriXK+O1kd4E1POqr4KifgXHTCja5BBNstnBIIcssbcSKbxIIyoVo7OToqUMyTbOJEG/7zeKJ15qMP5IwbEFNqkjDmLbqLBsqu6XCV8On+wCag2JyBFaJMeAoe9L0TbrKICb5bOCUQ7OYlsrML7GR8gQvG2pC+4sjoT9E/dgX70Z2ILkIG1xzSACC6igZoQ0TsfN5REB/EwbxBs33ytEOenxGcM/YocgTbLZwSCHKTjbBJNjjRyJNY3ea2ScI8wSbKdJtQUF76dZQXFIUc2wm45rBKgGTfzdR/B/fLByJhXHsEy6ZcRzFqZoIRfREDZd6im2iwSQ4T2fhRrP/SW4SX4V+ZsFog1K0RCZgxD18isaWsiwuswIe5HCNA1t2IneXe54CPyx6YN2hGHZhSvyXPL1UyYLQItls4JRDkBhmBIpvEwoKATA/MJrjuYUjASDqzvKug36HxrcrzmvIoNUmQPE/TE91FAyVFNoEdrta0T6hDni6PdojzE7SbE25yNUrtd2XesBtmKE+yswvsZLLC6iA2EwmY0Z+j/xVakqpwEYwAWXcjtjTFOsGHMCAwaLYvg3bI75kAzhm/j17qgFc8sSan+BhVOB95kU0OUHgAQv3CSTYZX4dqvSZwm1DQ9HuaS/Et4JrDGgEWOeDb26Rb8MsHaI+M7Ix8z35VUFJgS4osx7NykuWCcP0ywXYNRYJlzNUTCkpGcYEVjm2SqqOaYg37c8iq+klHxb3KCVg2J9yjuirgOJEn2Sg5XBBUV6IfqcKdAUsy74jez3sXSz2/APVPTTKTBPGVDsBO5XITl0k04OJNH12N8LEnnm0IEBg02xdJO+T5CeCc8ad8508mxVoq84XdYMNN7gqR5QJwDA2w3cOQgJT+kNgIKcUnWSFnc0kGgKyrAVfk/RDfF0uIt1h/7BwoxXdmgyF5Zm2UOUNukNEoO1Jk+QAcXx3+cBTgABLWZN6AwBb9AEJxgRUmZJKu+SZZc2LCBHe6ix0TVIrvxAZ3C6cEEt1Eg2h/pUU2iacLJGvlBNc1DAmWMQ9fIrFlImb4Uu7M9GMuxwiw5BTrMBTG5YPghDClriCovnMgQoxXar/TBBI06rDjIntUAqtomMj6z3IXGwyYukUV+zt1N2h+gjACiOWKBnLpUYsO1wg/rF71pEPHpx2ESX76sjN+D5/ZYMoVJJTS8OCl44MQWV0AnDOfRjcwmF0n84SCEtHTXJJvalRHM+/sDHkvOcUa0h4SFPYJCAyaFXucD+KrEgJ6JYHrFk4BBLlARsR1QZRw05+bBfIHAusahiQms/6mT+4ArbcxmUVgD/GqgFxHlO/pZKUME5tg6e+LVfW9MmFxYHXCTa5DZJKl1yzmoMxbdBMNstmHk2wSzxYonUUpwjNikqMEEFrTowkUuxTXhyiwO8JKcTI44B02+jhLImfC1L3/ZptiI/1H151aJCUQ7AYbBgmORfatbyUq0AfI921JgVWIbCSd010sozzKPhHdRAMmyYGtj8DGJllKCOnyaAdqG8Lv56VZz2wwGsthkh0uXDa+jW9AZG0BZO58Gj2MQ+YtmKsnFJmAyX4k80bxN3YZbHNYA0DW1YALcePz3thwv4J5g2bjUlROcTYCKEfsIGIE2y2cEkh0Ew3ELgNEVjERimETj4D2ejpgEihxopknFJSHPl0ybxS/t0vgm8MaAUR30YBmYnSyKeFj/XG6i80txYRoJe/mgSohP3Fc1WX5CAoQWTcmJjWUIhxmEBoO1BMKs+1nEdlSAms8hMX2Eg0Ypge2Ha4T/BiGACVMJ488yrpDCvb/QidD5BDBdgmnBIHdYMMkF4LI2gPkV8G30dXNEtH7010ssX/Qz+RPIcMTLH8QMyllbbvABX6FlhJCenvRDtD2U+8beD1KHvCKQ17sO+WgFuneVkcWFNkB8a69Q4IJ5uHLJL7UiEtOsYr+jD9SSUVmXs/S6cz1KI3TXSz0KGdY2qx4uy2tEkh0Ew1gZhYS2cjoCKecM4SPqP3AEblyGaIU/xXaTSZ+Cxpnp9QkYcxbdBcNlF1je4Iy9wfZpu6TZhC/FM+JidAtnBIIdoMNo324ozkjsrYA8HOKexgSMKM/qo9GMtv+RZ5ixWUSDRimB7Y2gZ0GDbME8wbN3AU2qnt0MgT3EWy3cAog2AU2zHLRoqxf+XRVn1/WdfPscXPVv0/WJ0A6um+jqxtxUqZ5QiFabzzAluJbwDWHVQIMiU7OACyZhH2Bu1j6XQVEuu5POsGyKddRrCEzwYi+iAGZt9h3ickbSWVyjA+fgBOTLJk8lcQA2zUMCUbrD4kvcdLDOeOO4mawzWGNAFl3I7Y0WzjAh/uVACVM1QNEioPZBLZNIFKoqvZYMUogyA0yknZ4P0fNPMlOkverpSsYBEycaMN/Ln4XuwkA5gstJ9GI5rBGANFdNFAy0uEq4dP9QU5AdHzaIc5P0PdOuMnVKLXflXnDbrBh8jlMmGRtAfLdP8B2D0MCJsxD/SVx0e3fwxbCP8hPdyWmH5RT0c7nHQXxLMF1BM3cJ9joOUAnIzKczdstnBIIcoOMRB6GOjKcZJ9evfXtO6K3ycA2SaRDk8REDtj4+2LJCQXlZkmBddExku8hjdkP0ChxYd4P7ZcPwiMfLTU5Yo1eKMV3ZoIxFWDcj2K5okE2++FqTSbZq/r88qL9w9cPrn7w5merm2sXGkKQ2z/FmiYUlLXiAiuoqK2P7G83y8Y3J5dZBZ8DPi57YN6g2bgIlVN2+21fdMLF1NwxnDJvyA0ygnZ6j7RaVeuXP/WD+vzy51qR/Zdnb//vrzdPP4JAOKPI6MgBCNYkOfAkS+JKNfU71HlCCeKWmiSMfIjuooHEcOJ1m8CmJpR9tmDeoNm+CNohXn/Qbk64ydUotd+VecNusGHyaAm+d/r8TrV++PK/1nce/kbdPHvypesP3/2L6/d+rGzknNsgeVsdkSAkYEZ/widZEhthrocsgC1NKOaQRoCsuxFbepBzgg9hQGDQzF1gd4BBfDohpLvTE7JLOCUI5AYZiRxMz7P6wcNqdffBl+rzy7+sm5/+5OebevXN5z/67kVzfe0TsU+pgwtOVDHnhSbYZGfaEl5SYF2eEI1tkXQ34kqr0r6Lw/BlSsO2Vb3rRF2e2nHMyFEIrKGBRRpFA6nD+vFm2B+r9bqpH73ytL5z9it1ff/7myjNs8d/ffPkgz9+/u4PfaLGJitHZPqiJxNbPaFA9J9+WiZ/BejaFJMVGRzw6FolnpfUTzqq8lROYeazCWxiMHEqg97r1JzkluSozVeXLzX1xb2/rs8v/6Q/Ipq3376sPnH/n5+/9/av3jx+z9CSpQmnGEw+gZknFJShYIL3W1RhZNm+bH6CMOYruosGKNPFBVb1UWu6PNohz89sQjsJZO475T6n3Wx8x3Skunu/Wj949O/V2YPfrOv6yWgOb5qPPlc9u/7H6w9/8vnr99/pnrOUPT7UdVsdidmCyCsRP94H7snO8COJQs7mkgwAWVcDrrj8h/i+WMNjcYyPpQSWFrrcYip7BHKDjJBO62+kVvcfNvXdB69X56vfruv7b0WpaJr3P1k9X/1t8+zqd6/ff2d98/FHVdXciIGSzypuJ5py5QCBVU0oKCN9fJ8FTYfNFIrmGrUz5p10N+JKNZ3uYuOTvcQb/Xpig7strxJIdBMNICY21dd1VZ9fVKt7l9f1+fnXqvXz1+r6pXeHAEG0pmnq6vmT36qq6k+bm+tfa64+fljd3FTNSGzHbtGxGUrzlhotJQ63hg5lE7ofqnMToqx7rjSLp1c8wFxMJeJ4DiN1Va/qqlqffViv1v9WVdVfVWcP/qmu63ZuG/2XZbVpPnilqta/UF1df65arx4tzNAp/ImBEwMnBm4JA/X71U31VnV+/d26fvROLqn/BxyYTjrvEz1RAAAAAElFTkSuQmCC);
  background-size: cover;
  padding: 22rpx;
  position: relative;
}
.welfare .top.data-v-fe340868 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.welfare .top .left.data-v-fe340868 {
  display: flex;
  align-items: center;
  flex: 1;
}
.welfare .top .left text.data-v-fe340868 {
  margin-left: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #451815;
}
.welfare .top .btn.data-v-fe340868 {
  width: 124rpx;
  height: 46rpx;
  background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);
  border-radius: 24rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 14rpx;
  flex-shrink: 0;
}
.welfare .bottom.data-v-fe340868 {
  width: 646rpx;
  margin-top: 12rpx;
  display: flex;
  border-radius: 12rpx;
  background-color: #fff;
}
.welfare .bottom .right.data-v-fe340868 {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.welfare .bottom .right .right_item.data-v-fe340868 {
  width: 136rpx;
  height: 198rpx;
  background: linear-gradient(180deg, #FEF7EC 0%, #FCEFDF 100%);
  border-radius: 12rpx;
  padding-top: 28rpx;
}
.welfare .bottom .right .right_item .box1.data-v-fe340868 {
  font-size: 35rpx;
  font-weight: 600;
  color: #E55138;
  text-align: center;
}
.welfare .bottom .right .right_item .box1 ._span.data-v-fe340868 {
  font-size: 24rpx;
}
.welfare .bottom .right .right_item .box2.data-v-fe340868 {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #E55138;
  text-align: center;
}
.welfare .bottom .right .right_item .box3.data-v-fe340868 {
  margin: 12rpx auto 0;
  width: 98rpx;
  height: 36rpx;
  background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);
  border-radius: 18rpx;
  font-size: 16rpx;
  color: #FFFFFF;
  line-height: 36rpx;
  text-align: center;
}
.service.data-v-fe340868 {
  margin-top: 40rpx;
}
.service .head.data-v-fe340868 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.service .head .left.data-v-fe340868 {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}
.service .head .right.data-v-fe340868 {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}
.service .se_main.data-v-fe340868 {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 20rpx;
}
.service .se_main .se_item.data-v-fe340868 {
  width: 336rpx;
  height: 428rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  border: 2rpx solid #F3F3F3;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding-top: 20rpx;
  margin-bottom: 20rpx;
}
.service .se_main .se_item .lbox.data-v-fe340868 {
  margin-top: 10rpx;
}
.service .se_main .se_item .lbox .name.data-v-fe340868 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
}
.service .se_main .se_item .lbox .baojia.data-v-fe340868 {
  margin: 6rpx auto 0;
  width: 140rpx;
  height: 50rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  border: 2rpx solid #2E80FE;
  font-size: 24rpx;
  color: #2E80FE;
  line-height: 50rpx;
  text-align: center;
}
.tips.data-v-fe340868 {
  margin-top: 32rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
}
.tips2.data-v-fe340868 {
  font-size: 24rpx;
  color: #333333;
  margin-top: 16rpx;
  text-align: center;
}

