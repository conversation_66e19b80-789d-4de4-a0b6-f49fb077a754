{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/huodongaddress.vue?e07b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/huodongaddress.vue?40cf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/huodongaddress.vue?aef4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/huodongaddress.vue?ebea", "uni-app:///user/huodongaddress.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/huodongaddress.vue?a552", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/huodongaddress.vue?8451"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "addressArr", "page", "limit", "total", "loading", "onPullDownRefresh", "console", "setTimeout", "uni", "onReachBottom", "methods", "goUrl", "url", "goUrlAdd", "choose", "getAddressList", "pageNum", "pageSize", "res", "newList", "loadMore", "onShow"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA61B,CAAgB,62BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoCj3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;IACA;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAL;MACAA;MACAE;MACA;MACAA;QACAI;MACA;IACA;IACAC;MACAL;QACAI;MACA;IACA;IACAE;MACA;MACA;MACA;QACAN;QACAA;MACA;QACA;MACA;IACA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAIAZ;gBACA;gBACA;gBACAa;kBAAA;gBAAA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAAomD,CAAgB,wjDAAG,EAAC,C;;;;;;;;;;;ACAxnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/huodongaddress.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/huodongaddress.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./huodongaddress.vue?vue&type=template&id=d480fd58&scoped=true&\"\nvar renderjs\nimport script from \"./huodongaddress.vue?vue&type=script&lang=js&\"\nexport * from \"./huodongaddress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./huodongaddress.vue?vue&type=style&index=0&id=d480fd58&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d480fd58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/huodongaddress.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongaddress.vue?vue&type=template&id=d480fd58&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.addressArr.length\n  var g1 = _vm.addressArr.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongaddress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongaddress.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"\" v-if=\"addressArr.length>0\">\n\t\t\t<view class=\"address_item\" v-for=\"(item,index) in addressArr\" :key=\"index\" @click=\"choose(item)\">\n\t\t\t\t<view class=\"head\">\n\t\t\t\t\t<view class=\"mr\" v-if=\"item.status == 1\">默认</view>\n\t\t\t\t\t<text>{{item.address}}</text>\n\t\t\t\t\t<span></span>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"body\">\n\t\t\t\t\t<view class=\"left\">{{item.addressInfo}}</view>\n\t\t\t\t\t<view class=\"right\" @click.stop=\"goUrl(item)\">\n\t\t\t\t\t\t<image src=\"../static/images/9369.png\" mode=\"\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"foot\">\n\t\t\t\t\t<view class=\"box\">{{item.userName}}</view>\n\t\t\t\t\t<view class=\"box\">{{item.sex == 1?'（先生）':'（女士）'}}</view>\n\t\t\t\t\t<view class=\"box\">{{item.mobile}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- <u-empty\n\t\t\t\tv-else\n\t\t\t\tmode=\"address\"\n\t\t\t\ticon=\"http://cdn.uviewui.com/uview/empty/address.png\"\n\t\t>\n\t\t</u-empty> -->\n\t\t<view v-if=\"addressArr.length < total\" class=\"load-more\" @click=\"loadMore\">加载更多</view>\n\t\t<view class=\"footer\">\n\t\t\t<view class=\"btn\" @tap=\"goUrlAdd('../user/add_address')\">新增地址</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\taddressArr: [],\n\t\t\tpage: 1,\n\t\t\tlimit: 10,\n\t\t\ttotal: 0, // Initialize total count for pagination\n\t\t\tloading: false // Prevent multiple simultaneous requests\n\t\t}\n\t},\n\tonPullDownRefresh() {\n\t\tconsole.log('refresh');\n\t\t// Reset to first page on pull-down refresh\n\t\tthis.page = 1;\n\t\tthis.addressArr = [];\n\t\tthis.getAddressList();\n\t\tsetTimeout(() => {\n\t\t\tuni.stopPullDownRefresh();\n\t\t}, 1000);\n\t},\n\tonReachBottom() {\n\t\t// Triggered when user scrolls to the bottom\n\t\tif (this.addressArr.length < this.total && !this.loading) {\n\t\t\tthis.page += 1;\n\t\t\tthis.getAddressList();\n\t\t}\n\t},\n\tmethods: {\n\t\tgoUrl(item) {\n\t\t\tconsole.log(item)\n\t\t\tconsole.log(item.id)\n\t\t\tuni.setStorageSync(\"editAdress\", item);\n\t\t\tconst e = `../user/edit_address?id=${item.id}`\n\t\t\tuni.navigateTo({\n\t\t\t\turl: e\n\t\t\t})\n\t\t},\n\t\tgoUrlAdd(e) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: e\n\t\t\t})\n\t\t},\n\t\tchoose(e) {\n\t\t\tlet pages = getCurrentPages() // 获取栈实例\n\t\t\tlet prevPage = pages[pages.length - 2] // 获取上个页面的数据，包含页面\n\t\t\tif (prevPage.route == 'user/huodong_order') {\n\t\t\t\tuni.$emit('chooseAddress', e)\n\t\t\t\tuni.navigateBack()\n\t\t\t} else {\n\t\t\t\treturn\n\t\t\t}\n\t\t},\n\t\tasync getAddressList() {\n\t\t\tif (this.loading) return; // Prevent multiple requests\n\t\t\tthis.loading = true;\n\t\t\ttry {\n\t\t\t\tlet res = await this.$api.mine.addressList({\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: this.limit\n\t\t\t\t});\n\t\t\t\tconsole.log(res);\n\t\t\t\tthis.total = res.totalCount; // Update total count\n\t\t\t\t// Append new items to the list or reset if page 1\n\t\t\t\tconst newList = res.list.sort((a, b) => b.status - a.status);\n\t\t\t\tthis.addressArr = this.page === 1 ? newList : [...this.addressArr, ...newList];\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\tloadMore() {\n\t\t\t// Manual load more button\n\t\t\tif (this.addressArr.length < this.total && !this.loading) {\n\t\t\t\tthis.page += 1;\n\t\t\t\tthis.getAddressList();\n\t\t\t}\n\t\t}\n\t},\n\tonShow() {\n\t\tthis.page = 1; // Reset page on show\n\t\tthis.addressArr = []; // Clear list\n\t\tthis.getAddressList();\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\theight: 100vh;\n\tpadding: 40rpx 0;\n\toverflow: auto;\n\tpadding-bottom: 200rpx;\n\tbackground-color: #f8f8f8;\n\n\t.address_item {\n\t\tbackground-color: #fff;\n\t\tpadding: 18rpx 30rpx;\n\t\tmargin-bottom: 20rpx;\n\n\t\t.head {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.mr {\n\t\t\t\twidth: 72rpx;\n\t\t\t\theight: 38rpx;\n\t\t\t\tbackground: #CCE0FF;\n\t\t\t\tborder-radius: 4rpx 4rpx 4rpx 4rpx;\n\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t\tline-height: 38rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\ttext {\n\t\t\t\tmargin-left: 36rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\n\t\t\tspan {\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\t\t}\n\n\t\t.body {\n\t\t\tmargin-top: 18rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\n\t\t\t.left {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333333;\n\t\t\t\tmax-width: 500rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\n\t\t\t.right {\n\t\t\t\timage {\n\t\t\t\t\twidth: 32rpx;\n\t\t\t\t\theight: 32rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.foot {\n\t\t\tmargin-top: 20rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #999999;\n\n\t\t\t.box {\n\t\t\t\tmargin-right: 15rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.load-more {\n\t\ttext-align: center;\n\t\tpadding: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #2e80fe;\n\t\tcursor: pointer;\n\t}\n\n\t.footer {\n\t\tpadding: 52rpx 30rpx;\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tbackground-color: #fff;\n\n\t\t.btn {\n\t\t\twidth: 690rpx;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongaddress.vue?vue&type=style&index=0&id=d480fd58&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./huodongaddress.vue?vue&type=style&index=0&id=d480fd58&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756102017826\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}