<view class="page data-v-99ba6ec0"><block wx:if="{{flag}}"><u-picker vue-id="9d926734-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeH<PERSON>ler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-99ba6ec0 vue-ref" bind:__l="__l"></u-picker></block><view class="top data-v-99ba6ec0">个人信息隐私信息完全保密</view><view class="main data-v-99ba6ec0"><block wx:if="{{hasLocationPermission}}"><view data-event-opts="{{[['tap',[['goMap',['$event']]]]]}}" class="main_item data-v-99ba6ec0" bindtap="__e"><view class="name data-v-99ba6ec0">服务地址</view><view class="address data-v-99ba6ec0"><label class="_span data-v-99ba6ec0">{{form.address}}</label></view><image src="../static/images/position.png" mode class="data-v-99ba6ec0"></image></view></block><block wx:if="{{!hasLocationPermission}}"><view class="main_item data-v-99ba6ec0"><view class="name data-v-99ba6ec0">所在区域</view><input type="text" placeholder="请选择所在区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e1',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-99ba6ec0"/></view></block><view class="main_item data-v-99ba6ec0"><view class="name data-v-99ba6ec0">门牌号</view><input type="text" placeholder="请输入详细地址，如7栋4单元18a" data-event-opts="{{[['input',[['__set_model',['$0','houseNumber','$event',[]],['form']]]]]}}" value="{{form.houseNumber}}" bindinput="__e" class="data-v-99ba6ec0"/></view><view class="main_item data-v-99ba6ec0"><view class="name data-v-99ba6ec0">联系人</view><input type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','userName','$event',[]],['form']]]]]}}" value="{{form.userName}}" bindinput="__e" class="data-v-99ba6ec0"/></view><view class="main_item data-v-99ba6ec0"><view class="name data-v-99ba6ec0">性别</view><view class="box data-v-99ba6ec0"><view data-event-opts="{{[['tap',[['selectGender',[1]]]]]}}" class="{{['box_item','data-v-99ba6ec0',(form.sex==1||form.sex==='1')?'selected':'']}}" style="{{(form.sex==1||form.sex==='1'?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':'')}}" bindtap="__e">先生</view><view data-event-opts="{{[['tap',[['selectGender',[2]]]]]}}" class="{{['box_item','data-v-99ba6ec0',(form.sex==2||form.sex==='2')?'selected':'']}}" style="{{(form.sex==2||form.sex==='2'?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':'')}}" bindtap="__e">女士</view></view></view><view class="main_item data-v-99ba6ec0"><view class="name data-v-99ba6ec0">手机号码</view><input type="tel" placeholder="请输入手机号码" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e" class="data-v-99ba6ec0"/></view><view class="main_item last data-v-99ba6ec0"><view class="name data-v-99ba6ec0">设为默认地址</view><u-switch bind:input="__e" vue-id="9d926734-2" activeColor="#2E80FE" value="{{form.status}}" data-event-opts="{{[['^input',[['__set_model',['$0','status','$event',[]],['form']]]]]}}" class="data-v-99ba6ec0" bind:__l="__l"></u-switch></view></view><view data-event-opts="{{[['tap',[['SaveAddress',['$event']]]]]}}" class="btn data-v-99ba6ec0" bindtap="__e">保存</view></view>