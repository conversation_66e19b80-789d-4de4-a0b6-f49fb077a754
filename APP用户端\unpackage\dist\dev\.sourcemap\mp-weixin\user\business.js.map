{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/business.vue?aa74", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/business.vue?17a4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/business.vue?80d6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/business.vue?55fd", "uni-app:///user/business.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/business.vue?6f11", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/business.vue?697e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "imgUrl", "infos", "inviteCode", "info", "created", "withShareTicket", "menus", "success", "console", "fail", "onShareAppMessage", "title", "path", "imageUrl", "methods", "saveImageWithPermission", "uni", "icon", "checkAuthStatus", "that", "requestPermission", "content", "showCancel", "confirmText", "cancelText", "downloadAndSaveImage", "mask", "url", "filePath", "errorMsg", "saveToAlbum", "duration", "setTimeout", "recordSaveAction", "saveimg", "getImg", "copyInviteCode", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAu1B,CAAgB,u2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmB32B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAV;MACAW;MACAC;MACAC;QACAC;MACA;MACAC;QACAD;MACA;IACA;EAEA;EACAE;IACA;IACA;MACAC;MACAC;MACAC;IACA;IACAL;IACA;EACA;EACAM;IACA;IACAC;MACA;QACAC;UACAL;UACAM;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEAF;QACAT;UACA;YACA;YACAY;UACA;YACA;YACAA;UACA;YACA;YACAA;UACA;QACA;QACAV;UACAD;UACAQ;YACAL;YACAM;UACA;QACA;MACA;IACA;IAEA;IACAG;MACA;MAEAJ;QACAL;QACAU;QACAC;QACAC;QACAC;QACAjB;UACA;YACA;YACAS;cACAT;gBACA;gBACA;kBACAY;gBACA;kBACAH;oBACAL;oBACAM;kBACA;gBACA;cACA;cACAR;gBACAD;gBACAQ;kBACAL;kBACAM;gBACA;cACA;YACA;UACA;YACA;YACAD;cACAL;cACAM;YACA;UACA;QACA;MACA;IACA;IAEA;IACAQ;MACA;;MAEA;MACAT;QACAL;QACAe;MACA;MAEAV;QACAW;QACA;QACAC;QACArB;UACA;YACA;YACAY;UACA;YACAH;YACAR;YACAQ;cACAL;cACAM;YACA;UACA;QACA;QACAR;UACAO;UACAR;;UAEA;UACA;UACA;YACA;cACAqB;YACA;cACAA;YACA;UACA;UAEAb;YACAL;YACAM;UACA;QACA;MACA;IACA;IAEA;IACAa;MACA;MAEAd;QACAY;QACArB;UACAS;UACAR;UACAQ;YACAL;YACAM;YACAc;UACA;;UAEA;UACA;UACAZ;QACA;QACAV;UACAO;UACAR;;UAEA;UACA;UACA;YACA;cACAqB;cACA;cACAG;gBACAb;cACA;YACA;cACAU;YACA;UACA;UAEAb;YACAL;YACAM;UACA;QACA;MACA;IACA;IAEA;IACAgB;MACA;MACA;MACAzB;IACA;IAEA;IACA0B;MACA;QACAlB;UACAL;UACAM;QACA;QACA;MACA;MACAD;QACAW;QACApB;UACA;YACAS;cACAY;cACArB;gBACAS;kBACAL;kBACAM;gBACA;cACA;cACAR;gBACAD;gBACAQ;kBACAL;kBACAM;gBACA;cACA;YACA;UACA;YACAT;YACAQ;cACAL;cACAM;YACA;UACA;QACA;QACAR;UACAD;UACAQ;YACAL;YACAM;UACA;QACA;MACA;IACA;IAEAkB;MAAA;MACA;QACA3B;QACA;QACA;MACA;QACAA;QACAQ;UACAL;UACAM;QACA;MACA;IACA;IAEAmB;MACA;QACApB;UACAL;UACAM;QACA;QACA;MACA;MACAD;QACAjB;QACAQ;UACAS;YACAL;YACAM;UACA;QACA;QACAR;UACAD;UACAQ;YACAL;YACAM;UACA;QACA;MACA;IACA;EACA;EACAoB;IACA7B;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1UA;AAAA;AAAA;AAAA;AAA8lD,CAAgB,kjDAAG,EAAC,C;;;;;;;;;;;ACAlnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/business.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/business.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./business.vue?vue&type=template&id=3b71b8e6&scoped=true&\"\nvar renderjs\nimport script from \"./business.vue?vue&type=script&lang=js&\"\nexport * from \"./business.vue?vue&type=script&lang=js&\"\nimport style0 from \"./business.vue?vue&type=style&index=0&id=3b71b8e6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3b71b8e6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/business.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./business.vue?vue&type=template&id=3b71b8e6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./business.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./business.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">邀请好友  赚奖励</view>\n\t\t<view class=\"box\">\n\t\t\t<view class=\"name\">{{info.name || ''}}</view>\n\t\t\t<view class=\"desc\">每邀请一位师傅和商家入驻，师傅、商家订单验收完结后，邀请人每单可获得1%的奖励。</view>\n\t\t\t<image :src=\"imgUrl\" mode=\"\"></image>\n\t\t\t<view class=\"invite-code-container\">\n\t\t\t\t<!-- <view class=\"invite-code\">邀请码: {{ inviteCode|| '无'}}</view> -->\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"button-container\">\n\t\t\t<view class=\"btn save-btn\" @click=\"saveImageWithPermission\">保存图片</view>\n\t\t\t<button class=\"btn share-btn\" open-type=\"share\" :disabled=\"!imgUrl\">分享</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\timgUrl: '',\n\t\t\tinfos:'',\n\t\t\tinviteCode:'',\n\t\t\tinfo: {}\n\t\t}\n\t},\n\tcreated() {\n\t\t//#ifdef MP-WEIXIN\n\t\twx.showShareMenu({\n\t\t\twithShareTicket: true,\n\t\t\tmenus: ['shareAppMessage', 'shareTimeline'],\n\t\t\tsuccess: () => {\n\t\t\t\tconsole.log('Share menu enabled');\n\t\t\t},\n\t\t\tfail: (e) => {\n\t\t\t\tconsole.error('Failed to enable share menu:', e);\n\t\t\t}\n\t\t});\n\t\t//#endif\n\t},\n\tonShareAppMessage(res) {\n\t\tconst inviteCode = this.inviteCode || '';\n\t\tconst shareData = {\n\t\t\ttitle: '邀好友，赚现金.邀请师傅和商家，接单下单都赚钱',\n\t\t\tpath: `/pages/mine?inviteCode=${inviteCode}`,\n\t\t\timageUrl: this.imgUrl || ''\n\t\t};\n\t\tconsole.log('Sharing with:', shareData);\n\t\treturn shareData;\n\t},\n\tmethods: {\n\t\t// 带权限检查的保存图片方法\n\t\tsaveImageWithPermission() {\n\t\t\tif (!this.imgUrl) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '图片未加载',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查授权状态\n\t\t\tthis.checkAuthStatus();\n\t\t},\n\t\t\n\t\t// 检查用户授权状态\n\t\tcheckAuthStatus() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\tuni.getSetting({\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tif (res.authSetting['scope.writePhotosAlbum']) {\n\t\t\t\t\t\t// 已授权，直接开始下载保存\n\t\t\t\t\t\tthat.downloadAndSaveImage();\n\t\t\t\t\t} else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {\n\t\t\t\t\t\t// 首次请求，还未授权过，直接开始下载（会自动弹出授权）\n\t\t\t\t\t\tthat.downloadAndSaveImage();\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 用户曾经拒绝过授权，需要引导用户手动开启\n\t\t\t\t\t\tthat.requestPermission();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: function(error) {\n\t\t\t\t\tconsole.log(error, 'getSetting');\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取授权状态失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 请求用户授权\n\t\trequestPermission() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '授权提示',\n\t\t\t\tcontent: '需要您授权保存图片到相册，请在设置中开启相册权限',\n\t\t\t\tshowCancel: true,\n\t\t\t\tconfirmText: '去设置',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: function(resShow) {\n\t\t\t\t\tif (resShow.confirm) {\n\t\t\t\t\t\t// 用户点击去设置，打开设置页面\n\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\tsuccess: function(resOpen) {\n\t\t\t\t\t\t\t\t// 用户在设置页面操作完成后，重新检查权限\n\t\t\t\t\t\t\t\tif (resOpen.authSetting['scope.writePhotosAlbum']) {\n\t\t\t\t\t\t\t\t\tthat.downloadAndSaveImage();\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '未开启相册权限',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(errorOpen) {\n\t\t\t\t\t\t\t\tconsole.log(errorOpen, 'openSetting');\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '打开设置失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 用户取消授权\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '取消授权无法保存',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 下载并保存图片\n\t\tdownloadAndSaveImage() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\t// 显示加载提示\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '保存中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\tuni.downloadFile({\n\t\t\t\turl: this.imgUrl,\n\t\t\t\t// 生成唯一的文件名\n\t\t\t\tfilePath: uni.env.USER_DATA_PATH + '/invite_' + new Date().getTime() + '.jpg',\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t// 下载成功，保存到相册\n\t\t\t\t\t\tthat.saveToAlbum(res.filePath);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tconsole.error('Download failed with status:', res.statusCode);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片下载失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail(error) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tconsole.error('Download error:', error);\n\t\t\t\t\t\n\t\t\t\t\t// 根据错误类型给出不同提示\n\t\t\t\t\tlet errorMsg = '图片下载失败';\n\t\t\t\t\tif (error.errMsg) {\n\t\t\t\t\t\tif (error.errMsg.includes('network')) {\n\t\t\t\t\t\t\terrorMsg = '网络连接失败';\n\t\t\t\t\t\t} else if (error.errMsg.includes('timeout')) {\n\t\t\t\t\t\t\terrorMsg = '下载超时';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 保存图片到系统相册\n\t\tsaveToAlbum(filePath) {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\tfilePath: filePath,\n\t\t\t\tsuccess: function() {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tconsole.log('图片保存成功');\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 可以在这里添加保存成功后的业务逻辑\n\t\t\t\t\t// 比如记录下载次数、统计等\n\t\t\t\t\tthat.recordSaveAction();\n\t\t\t\t},\n\t\t\t\tfail: function(error) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tconsole.error('Save image failed:', error);\n\t\t\t\t\t\n\t\t\t\t\t// 处理不同的保存失败情况\n\t\t\t\t\tlet errorMsg = '保存失败';\n\t\t\t\t\tif (error.errMsg) {\n\t\t\t\t\t\tif (error.errMsg.includes('auth deny')) {\n\t\t\t\t\t\t\terrorMsg = '保存失败，请检查相册权限';\n\t\t\t\t\t\t\t// 如果是权限问题，可以再次引导用户授权\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthat.requestPermission();\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t} else if (error.errMsg.includes('system deny')) {\n\t\t\t\t\t\t\terrorMsg = '系统拒绝保存';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 记录保存操作（可选的业务逻辑）\n\t\trecordSaveAction() {\n\t\t\t// 这里可以添加记录用户保存行为的逻辑\n\t\t\t// 比如上报统计数据、记录用户行为等\n\t\t\tconsole.log('用户保存了邀请图片');\n\t\t},\n\t\t\n\t\t// 原有的简单保存方法（保留作为备用）\n\t\tsaveimg() {\n\t\t\tif (!this.imgUrl) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '图片未加载',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.downloadFile({\n\t\t\t\turl: this.imgUrl,\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '图片保存成功',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (e) => {\n\t\t\t\t\t\t\t\tconsole.error('Save image failed:', e);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '图片保存失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('Download failed:', res);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片下载失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (e) => {\n\t\t\t\t\tconsole.error('Download error:', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '图片下载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tgetImg() {\n\t\t\tthis.$api.service.getPromoterCard().then(res => {\n\t\t\t\tconsole.log('QR code fetched:', res);\n\t\t\t\tthis.imgUrl = res.data.qrUrl;\n\t\t\t\tthis.inviteCode = res.data.qrCode;\n\t\t\t}).catch(e => {\n\t\t\t\tconsole.error('Failed to fetch QR code:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取二维码失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\tcopyInviteCode() {\n\t\t\tif (!this.info.inviteCode) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无邀请码',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: this.info.inviteCode,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制成功',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: (e) => {\n\t\t\t\t\tconsole.error('Copy failed:', e);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t},\n\tonLoad() {\n\t\tconsole.log('User info loaded:', this.info);\n\t\tthis.getImg();\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground: #f8f8f8;\n\t\theight: 100vh;\n\t\tpadding: 40rpx 30rpx;\n\n\t\t.header {\n\t\t\ttext-align: center;\n\t\t\tfont-size: 52rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #000000;\n\t\t}\n\n\t\t.box {\n\t\t\tmargin-top: 40rpx;\n\t\t\twidth: 690rpx;\n\t\t\theight: 748rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 32rpx;\n\t\t\tpadding: 40rpx 0;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\n\t\t\t.name {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #000000;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\t.desc {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #000000;\n\t\t\t\tpadding: 0 30rpx;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 444rpx;\n\t\t\t\theight: 444rpx;\n\t\t\t\tmargin: 18rpx auto 0;\n\t\t\t}\n\n\t\t\t.invite-code-container {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tgap: 20rpx;\n\t\t\t}\n\n\t\t\t.invite-code {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #000000;\n\t\t\t}\n\n\t\t\t.copy-btn {\n\t\t\t\twidth: 120rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tline-height: 60rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t}\n\t\t}\n\n\t\t.button-container {\n\t\t\tdisplay: flex;\n\t\t\tgap: 20rpx;\n\t\t\tposition: absolute;\n\t\t\tbottom: 42rpx;\n\t\t\twidth: 690rpx;\n\t\t}\n\n\t\t.btn {\n\t\t\tflex: 1;\n\t\t\theight: 98rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tborder-radius: 50rpx;\n\t\t\tline-height: 98rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #FFFFFF;\n\t\t\tcursor: pointer;\n\t\t\ttransition: all 0.3s ease;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground: #1E70EE;\n\t\t\t\ttransform: scale(0.98);\n\t\t\t}\n\t\t}\n\n\t\t.save-btn {\n\t\t\t/* Specific styles for save button if needed */\n\t\t}\n\n\t\t.share-btn {\n\t\t\t/* Ensure button inherits same styles */\n\t\t\tborder: none;\n\t\t\tpadding: 0;\n\t\t\tmargin: 0;\n\t\t\tbackground: #2E80FE;\n\t\t\t/* Remove default button styles */\n\t\t\t&:after {\n\t\t\t\tborder: none;\n\t\t\t}\n\t\t}\n\n\t\t.share-btn[disabled] {\n\t\t\tbackground: #cccccc;\n\t\t\tpointer-events: none;\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./business.vue?vue&type=style&index=0&id=3b71b8e6&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./business.vue?vue&type=style&index=0&id=3b71b8e6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756102021556\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}