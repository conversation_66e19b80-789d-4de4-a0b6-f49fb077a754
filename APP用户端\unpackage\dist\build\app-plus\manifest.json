{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__460500C", "name": "今师傅", "version": {"name": "1.1.1", "code": 111}, "description": "今师傅", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"OAuth": {}, "Geolocation": {}, "Maps": {"coordType": "gcj02"}, "Payment": {}, "Share": {}, "VideoPlayer": {}, "Camera": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": false, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#599eff"}, "checkPermissionDenied": true, "compatible": {"ignoreVersion": true}, "usingComponents": true, "privacy": {"prompt": "template"}, "distribute": {"permissionExternalStorage": {"request": "always", "prompt": "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"}, "permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_SURFACE_FLINGER\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png"}, "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "proapp@2x": "unpackage/res/icons/167x167.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png"}}}, "splashscreen": {"useOriginalMsgbox": true}, "google": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_SURFACE_FLINGER\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CLEAR_APP_USER_DATA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "schemes": "myapp,helloapp"}, "apple": {"deploymentTarget": "13.0", "dSYMs": false, "capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:https://zskj.asia"]}}, "idfa": false, "privacyDescription": {"NSPhotoLibraryUsageDescription": "用户申请服务时从相册中选择图片用以更好的让服务方了解情况", "NSPhotoLibraryAddUsageDescription": "保存用户所需要的图片", "NSCameraUsageDescription": "用户申请服务时使用摄像头中拍摄照片用以更好的让服务方了解情况", "NSLocationAlwaysAndWhenInUseUsageDescription": "获取用户地理位置用以服务方获取服务距离以及提供服务", "NSLocationWhenInUseUsageDescription": "获取用户地理位置用以服务方获取服务距离以及提供服务", "NSLocationAlwaysUsageDescription": "获取用户地理位置用以服务方获取服务距离以及提供服务"}, "urltypes": "myapp,helloapp", "urlschemewhitelist": "alipays,alipay,safepay,weixin,wechat"}, "plugins": {"oauth": {"weixin": {"appid": "wx5e2f07d307654aed", "appsecret": "d36e56321056e5ac5b7980854ba854ae", "UniversalLinks": "https://zskj.asia/ulink/"}, "apple": {}}, "geolocation": {"amap": {"__platform__": ["ios", "android"], "appkey_ios": "3277219165a595a8737fc2a611427e85", "appkey_android": "3277219165a595a8737fc2a611427e85", "name": "amap_18255871118"}}, "maps": {"amap": {"appkey_ios": "3277219165a595a8737fc2a611427e85", "appkey_android": "3277219165a595a8737fc2a611427e85"}}, "payment": {"weixin": {"__platform__": ["ios", "android"], "appid": "wx5e2f07d307654aed", "UniversalLinks": "https://zskj.asia/ulink/"}}, "share": {"weixin": {"appid": "wx5e2f07d307654aed", "UniversalLinks": "https://zskj.asia/ulink/"}}, "ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "ios": {}, "sdkConfigs": {"payment": {"weixin": {"__platform__": ["ios", "android"], "appid": "wx49c2324ccd5681b0", "UniversalLinks": "https://zskj.asia/ulink/"}}, "oauth": {"weixin": {"appid": "wx49c2324ccd5681b0", "appsecret": "cdc37c403a2d828fd8effcf384a42c92", "UniversalLinks": "https://zskj.asia/ulink/"}}, "push": {}, "share": {"weixin": {"appid": "wx49c2324ccd5681b0", "UniversalLinks": "https://zskj.asia/ulink/"}}, "ad": {}, "geolocation": {"amap": {"__platform__": ["android"], "appkey_ios": "", "appkey_android": "6cbbd69cc7973aba0d1c834accb2ceb3"}}, "maps": {"amap": {"appkey_ios": "6cbbd69cc7973aba0d1c834accb2ceb3", "appkey_android": "6cbbd69cc7973aba0d1c834accb2ceb3"}}}, "nativePlugins": {"JG-JPush": {"JPUSH_ADVERTISINGID_IOS": "", "JPUSH_DEFAULTINITJPUSH_IOS": "", "JPUSH_GOOGLE_API_KEY": "", "JPUSH_GOOGLE_APP_ID": "", "JPUSH_GOOGLE_PROJECT_ID": "", "JPUSH_GOOGLE_PROJECT_NUMBER": "", "JPUSH_GOOGLE_STORAGE_BUCKET": "", "JPUSH_HONOR_APPID": "", "JPUSH_HUAWEI_APPID": "", "JPUSH_ISPRODUCTION_IOS": "false", "JPUSH_MEIZU_APPID": "", "JPUSH_MEIZU_APPKEY": "", "JPUSH_OPPO_APPID": "31635145", "JPUSH_OPPO_APPKEY": "f3f513aa1f906542be110008", "JPUSH_OPPO_APPSECRET": "18d5d5393f204357b94529d1a9afe550", "JPUSH_VIVO_APPID": "105717228", "JPUSH_VIVO_APPKEY": "8c104ad0713b2811b8e240f1466785a2", "JPUSH_XIAOMI_APPID": "2882303761520298673", "JPUSH_XIAOMI_APPKEY": "5372029835673", "__plugin_info__": {"name": "极光推送 JPush 官方 SDK", "description": "极光推送JPush官方SDK HBuilder插件版本", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=4035", "android_package_name": "uni.UNIA2F404B", "ios_bundle_id": "", "isCloud": true, "bought": 1, "pid": "4035", "parameters": {"JPUSH_ADVERTISINGID_IOS": {"des": "[iOS]广告标识符（IDFA）如果不需要使用IDFA，可不填", "key": "JPush:ADVERTISINGID", "value": ""}, "JPUSH_DEFAULTINITJPUSH_IOS": {"des": "[iOS]是否默认初始化，是填true，不是填false或者不填", "key": "JPush:DEFAULTINITJPUSH", "value": ""}, "JPUSH_GOOGLE_API_KEY": {"des": "厂商google api_key,示例:g-12346578", "key": "google_api_key", "value": ""}, "JPUSH_GOOGLE_APP_ID": {"des": "厂商google mobilesdk_app_id,示例：g-12346578", "key": "google_app_id", "value": ""}, "JPUSH_GOOGLE_PROJECT_ID": {"des": "厂商google project_id ,示例：g-12346578", "key": "project_id", "value": ""}, "JPUSH_GOOGLE_PROJECT_NUMBER": {"des": "厂商google project_number,示例：g-12346578", "key": "gcm_defaultSenderId", "value": ""}, "JPUSH_GOOGLE_STORAGE_BUCKET": {"des": "厂商google storage_bucket,示例：g-12346578", "key": "google_storage_bucket", "value": ""}, "JPUSH_HONOR_APPID": {"des": "厂商HONOR-appId,示例：12346578", "key": "com.hihonor.push.app_id", "value": ""}, "JPUSH_HUAWEI_APPID": {"des": "厂商HUAWEI-appId,示例：appid=12346578", "key": "com.huawei.hms.client.appid", "value": ""}, "JPUSH_ISPRODUCTION_IOS": {"des": "[iOS]是否是生产环境，是填true，不是填false或者不填", "key": "JPush:ISPRODUCTION", "value": ""}, "JPUSH_MEIZU_APPID": {"des": "厂商MEIZU-appId,示例：MZ-12345678", "key": "MEIZU_APPID", "value": ""}, "JPUSH_MEIZU_APPKEY": {"des": "厂商MEIZU-<PERSON><PERSON><PERSON>,示例：MZ-12345678", "key": "MEIZU_APPKEY", "value": ""}, "JPUSH_OPPO_APPID": {"des": "厂商OPPO-appId,示例：OP-12345678", "key": "OPPO_APPID", "value": ""}, "JPUSH_OPPO_APPKEY": {"des": "厂商OPPO-appkey,示例：OP-12345678", "key": "OPPO_APPKEY", "value": ""}, "JPUSH_OPPO_APPSECRET": {"des": "厂商OPPO-appSecret,示例：OP-12345678", "key": "OPPO_APPSECRET", "value": ""}, "JPUSH_VIVO_APPID": {"des": "厂商VIVO-appId,示例：12345678", "key": "com.vivo.push.app_id", "value": ""}, "JPUSH_VIVO_APPKEY": {"des": "厂商VIVO-appkey,示例：12345678", "key": "com.vivo.push.api_key", "value": ""}, "JPUSH_XIAOMI_APPID": {"des": "厂商XIAOMI-appId,示例：MI-12345678", "key": "XIAOMI_APPID", "value": ""}, "JPUSH_XIAOMI_APPKEY": {"des": "厂商XIAOMI-<PERSON><PERSON><PERSON>,示例：MI-12345678", "key": "XIAOMI_APPKEY", "value": ""}}}}, "JG-JCore": {"JPUSH_APPKEY_ANDROID": "f3f513aa1f906542be110008", "JPUSH_APPKEY_IOS": "f3f513aa1f906542be110008", "JPUSH_CHANNEL_ANDROID": "developer-default", "JPUSH_CHANNEL_IOS": "developer-default", "__plugin_info__": {"name": "极光推送 JCore 官方 SDK", "description": "极光推送 JCore 官方 SDK HBuilder 插件版本", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=4028", "android_package_name": "uni.UNIA2F404B", "ios_bundle_id": "", "isCloud": true, "bought": 1, "pid": "4028", "parameters": {"JPUSH_APPKEY_ANDROID": {"des": "[Android]极光portal配置应用信息时分配的AppKey", "key": "JPUSH_APPKEY", "value": ""}, "JPUSH_APPKEY_IOS": {"des": "[iOS]极光portal配置应用信息时分配的AppKey", "key": "JCore:APP_KEY", "value": ""}, "JPUSH_CHANNEL_ANDROID": {"des": "[Android]用于统计分发渠道，不需要可填默认值developer-default", "key": "JPUSH_CHANNEL", "value": ""}, "JPUSH_CHANNEL_IOS": {"des": "[iOS]用于统计分发渠道，不需要可填默认值developer-default", "key": "JCore:CHANNEL", "value": ""}}}}}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "launch_path": "__uniappview.html"}}